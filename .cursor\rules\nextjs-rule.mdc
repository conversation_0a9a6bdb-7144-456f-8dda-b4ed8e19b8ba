---
description: 
globs: 
alwaysApply: true
---
You are an expert in TypeScript, Node.js, Next.js App Router, React, Shadcn UI, Radix UI, Supabase, Tailwind, and Vercel AI SDK.

**Code Style and Structure**

- Write concise, technical TypeScript code with accurate examples.
- Use functional and declarative programming patterns; avoid classes.
- Prefer iteration and modularization over code duplication.
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
- Structure files: exported component, subcomponents, helpers, static content, types.

**Naming Conventions**

- Use lowercase with dashes for directories (e.g., components/auth-wizard).
- Favor named exports for components.

**TypeScript Usage**

- Use TypeScript for all code; prefer interfaces over types.
- Avoid enums; use const objects or as const assertions instead.
- Use functional components with TypeScript interfaces.

**Syntax and Formatting**

- Use arrow functions for components and handlers.
- Avoid unnecessary curly braces in conditionals; use concise syntax for simple statements.
- Use declarative JSX.

**UI and Styling**

- Use Shadcn UI, Radix, and Tailwind for components and styling.
- Implement responsive design with Tailwind CSS; use a mobile-first approach.

**Performance Optimization**

- Minimize 'use client', 'useEffect', and 'useState'; favor React Server Components (RSC).
- Wrap client components in Suspense with fallback.
- Use dynamic loading for non-critical components.
- Optimize images: use Next.js Image component, include size data, implement lazy loading.

**Database Querying & Data Model Creation**

- Use Supabase SDK for data fetching and querying.
- For data model creation, use Supabase's schema builder.

**Key Conventions**

- Use 'nuqs' for URL search parameter state management.
- Optimize Web Vitals (LCP, CLS, FID).
- Limit 'use client':
  - Favor server components and Next.js SSR.
  - Use only for Web API access in small components.
  - Avoid for data fetching or state management.

**Vercel AI SDK Integration**

- Use Vercel AI SDK for building AI-powered features.
- Implement AI SDK Core for generating text, structured objects, and tool calls with LLMs.
- Utilize AI SDK UI hooks for building chat interfaces.
- Leverage AI SDK RSC for streaming generative user interfaces with React Server Components.

**Data Fetching and API Routes**

- Use Next.js App Router conventions for data fetching and API routes.
- Implement efficient caching and revalidation strategies using Next.js built-in features.
- Use route handlers (route.ts) for API routes in the App Router.

**Error Handling and Loading States**

- Implement error boundaries and error.tsx files for error handling.
- Use loading.tsx files for managing loading states.

**SEO and Metadata**

- Use Next.js latest version metadata API for SEO optimization.

**Follow Next.js docs for Data Fetching, Rendering, and Routing.**



