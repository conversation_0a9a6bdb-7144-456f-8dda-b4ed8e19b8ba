
import { PrintReport } from "@/components/reports/PrintReport";
import { AddSaleDialog } from "@/components/sales/AddSaleDialog";
import { AdvancePaymentDialog } from "@/components/sales/AdvancePaymentDialog";
import { EditSaleDialog } from "@/components/sales/EditSaleDialog";
import { SalesChart } from "@/components/sales/SalesChart";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useSupabaseCourses } from "@/hooks/useSupabaseCourses";
import { useSupabaseCustomers } from "@/hooks/useSupabaseCustomers";
import { SaleWithRelations, useSupabaseSales } from "@/hooks/useSupabaseSales";
import { useSupabaseTelemarketers } from "@/hooks/useSupabaseTelemarketers";
import { TimePeriod } from "@/types";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

export default function Sales() {
  const navigate = useNavigate();

  const { sales, addSale, updateSale, updateSaleStatus, addAdvancePayment } = useSupabaseSales();
  const { telemarketers } = useSupabaseTelemarketers();
  const { customers } = useSupabaseCustomers();
  const { courses } = useSupabaseCourses();

  const [selectedTelemarketerId, setSelectedTelemarketerId] = useState<string | null>(null);
  const [selectedTimePeriod, setSelectedTimePeriod] = useState<TimePeriod>("all");

  const validTelemarketers = Array.isArray(telemarketers) ? telemarketers : [];
  const validCustomers = Array.isArray(customers) ? customers : [];
  const validCourses = Array.isArray(courses) ? courses : [];

  const filteredSales = sales.filter(sale => {
    if (selectedTelemarketerId && sale.telemarketer_id !== selectedTelemarketerId) {
      return false;
    }
    if (selectedTimePeriod !== "all") {
      const now = new Date();
      const saleDate = new Date(sale.date);
      if (selectedTimePeriod === "day") {
        return saleDate.toDateString() === now.toDateString();
      } else if (selectedTimePeriod === "week") {
        const getWeekNumber = (d: Date) => {
          const date = new Date(d.getTime());
          date.setHours(0, 0, 0, 0);
          date.setDate(date.getDate() + 4 - (date.getDay() || 7));
          const yearStart = new Date(date.getFullYear(), 0, 1);
          return Math.ceil(((date.getTime() - yearStart.getTime()) / 86400000 + 1) / 7);
        };
        return getWeekNumber(saleDate) === getWeekNumber(now) && saleDate.getFullYear() === now.getFullYear();
      } else if (selectedTimePeriod === "month") {
        return saleDate.getMonth() === now.getMonth() && saleDate.getFullYear() === now.getFullYear();
      }
    }
    return true;
  });

  // Only include paid and advance status sales in the detail view
  const filteredDetailSales = filteredSales.filter(sale =>
    sale.status === "paid" || sale.status === "advance"
  );

  const handleAddAdvance = (saleId: string, amount: number) => {
    addAdvancePayment(
      { sale_id: saleId, amount },
      {
        onSuccess: () => {
          toast.success("Paiement partiel ajouté avec succès");
        },
        onError: (error) => {
          toast.error("Erreur lors de l'ajout du paiement");
          console.error("Error adding advance payment:", error);
        }
      }
    );
  };

  const updateSaleMarkAsPaid = (saleId: string) => {
    updateSaleStatus(
      { id: saleId, status: "paid" },
      {
        onSuccess: () => {
          toast.success("Vente marquée comme payée");
        },
        onError: (error) => {
          toast.error("Erreur lors de la mise à jour du statut de la vente");
          console.error("Error updating sale status:", error);
        }
      }
    );
  };

  const updateSaleStatus2Promise = (saleId: string) => {
    updateSaleStatus(
      {
        id: saleId,
        status: "promise",
        due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        onSuccess: () => {
          toast.success("Vente convertie en promesse. Redirection vers la page des promesses.");
          navigate('/promises');
        },
        onError: (error) => {
          toast.error("Erreur lors de la conversion en promesse");
          console.error("Error converting to promise:", error);
        }
      }
    );
  };

  const handleAddSale = (newSale: any) => {
    if (!newSale || !newSale.customer_id || !newSale.telemarketer_id || !newSale.course_ids || !newSale.courses) {
      toast.error("Données de vente invalides");
      return;
    }

    if (newSale.course_ids.length === 0 || newSale.courses.length === 0) {
      toast.error("Veuillez sélectionner au moins une formation");
      return;
    }

    // Validation pour les avances
    if (newSale.status === 'advance') {
      if (!newSale.advance_amount || parseFloat(newSale.advance_amount) <= 0) {
        toast.error("Veuillez spécifier un montant d'avance valide");
        return;
      }

      if (!newSale.advance_due_date) {
        toast.error("Veuillez spécifier une date limite pour le paiement complet");
        return;
      }
    }

    addSale(
      {
        customer_id: newSale.customer_id,
        telemarketer_id: newSale.telemarketer_id,
        course_ids: newSale.course_ids,
        courses: newSale.courses,
        price: newSale.price,
        original_price: newSale.original_price,
        discount: newSale.discount,
        status: newSale.status,
        date: newSale.date,
        due_date: newSale.status === 'advance' ? newSale.advance_due_date : newSale.due_date,
        advance_amount: newSale.status === 'advance' ? parseFloat(newSale.advance_amount) : null,
        advance_due_date: newSale.status === 'advance' ? newSale.advance_due_date : null
      },
      {
        onSuccess: () => {
          toast.success("Vente ajoutée avec succès");
        },
        onError: (error) => {
          toast.error("Erreur lors de l'ajout de la vente");
          console.error("Error adding sale:", error);
        }
      }
    );
  };

  const handleUpdateSale = (updatedSale: any) => {
    if (!updatedSale || !updatedSale.id || !updatedSale.customer_id || !updatedSale.telemarketer_id || !updatedSale.course_ids || !updatedSale.courses) {
      toast.error("Données de vente invalides");
      return;
    }

    if (updatedSale.course_ids.length === 0 || updatedSale.courses.length === 0) {
      toast.error("Veuillez sélectionner au moins une formation");
      return;
    }

    // Validation pour les avances
    if (updatedSale.status === 'advance') {
      if (!updatedSale.advance_amount || parseFloat(updatedSale.advance_amount) <= 0) {
        toast.error("Veuillez spécifier un montant d'avance valide");
        return;
      }

      if (!updatedSale.advance_due_date) {
        toast.error("Veuillez spécifier une date limite pour le paiement complet");
        return;
      }
    }

    updateSale(
      {
        id: updatedSale.id,
        customer_id: updatedSale.customer_id,
        telemarketer_id: updatedSale.telemarketer_id,
        course_ids: updatedSale.course_ids,
        courses: updatedSale.courses,
        price: updatedSale.price,
        original_price: updatedSale.original_price,
        discount: updatedSale.discount,
        status: updatedSale.status,
        date: updatedSale.date,
        due_date: updatedSale.status === 'advance' ? updatedSale.advance_due_date : updatedSale.due_date,
        advance_amount: updatedSale.status === 'advance' ? parseFloat(updatedSale.advance_amount) : null,
        advance_due_date: updatedSale.status === 'advance' ? updatedSale.advance_due_date : null
      },
      {
        onSuccess: () => {
          toast.success("Vente mise à jour avec succès");
        },
        onError: (error) => {
          toast.error("Erreur lors de la mise à jour de la vente");
          console.error("Error updating sale:", error);
        }
      }
    );
  };

  // Only include paid and advance statuses for telemarketer performance
  const salesByTelemarketer = sales
    .filter(sale => sale.status === "paid" || sale.status === "advance")
    .reduce((acc: Array<{ name: string; sales: number }>, sale) => {
      if (!sale.telemarketers.name) return acc;

      const existingTelemarketer = acc.find(t => t.name === sale.telemarketers.name);
      if (existingTelemarketer) {
        existingTelemarketer.sales += 1;
      } else {
        acc.push({ name: sale.telemarketers.name, sales: 1 });
      }
      return acc;
    }, []);

  const getStatusBadge = (sale: SaleWithRelations) => {
    return (
      <AdvancePaymentDialog
        sale={sale}
        onAddAdvance={handleAddAdvance}
        onMarkPaid={updateSaleMarkAsPaid}
      />
    );
  };



  const activeTelemarketers = validTelemarketers.filter(t => !t.archived);

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString("fr-FR", { style: "currency", currency: "XOF" });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Ventes</h1>
        <div className="flex items-center gap-2">

          <Select
            value={selectedTimePeriod}
            onValueChange={(value: TimePeriod) => setSelectedTimePeriod(value)}
          >
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Période" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Aujourd'hui</SelectItem>
              <SelectItem value="week">Cette semaine</SelectItem>
              <SelectItem value="month">Ce mois</SelectItem>
              <SelectItem value="all">Tout</SelectItem>
            </SelectContent>
          </Select>
          <PrintReport
            type="sales"
            data={filteredDetailSales}
            period={selectedTimePeriod}
            selectedTelemarketer={validTelemarketers.find(t => t.id === selectedTelemarketerId)}
          />
          <Button variant="outline" onClick={() => navigate('/promises')}>
            Voir les promesses
          </Button>
          <AddSaleDialog
            onSaleAdd={handleAddSale}
            telemarketers={activeTelemarketers}
            courses={validCourses}
            customers={validCustomers}
          />
        </div>
      </div>

      {/* Only render chart if we have data */}
      {salesByTelemarketer.length > 0 && (
        <SalesChart salesData={salesByTelemarketer} allSales={sales} />
      )}

      <Card>
        <CardHeader>
          <CardTitle>Ventes complétées</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Client</TableHead>
                <TableHead>Télévendeur</TableHead>
                <TableHead>Formation</TableHead>
                <TableHead>Prix</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {/* Filter out sales that are not paid or advance */}
              {sales.filter(sale => sale.status === "paid" || sale.status === "advance").map((sale) => (
                <TableRow key={sale.id}>
                  <TableCell>{new Date(sale.date).toLocaleDateString("fr-FR")}</TableCell>
                  <TableCell>
                    {sale.customers.name}
                    <div className="text-xs text-muted-foreground">
                      {sale.customers.email} • {sale.customers.phone}
                    </div>
                  </TableCell>
                  <TableCell>
                    {validTelemarketers.find(t => t.id === sale.telemarketer_id)?.archived ? (
                      <span className="italic text-muted-foreground">{sale.telemarketers.name} (Archivé)</span>
                    ) : (
                      sale.telemarketers.name
                    )}
                  </TableCell>
                  <TableCell>
                    {sale.courses.name}
                    {sale.sale_courses && sale.sale_courses.length > 1 && (
                      <div className="text-xs text-muted-foreground mt-1 cursor-help relative group">
                        + {sale.sale_courses.length - 1} autre(s) formation(s)
                        <div className="hidden group-hover:block absolute bg-popover p-2 rounded-md shadow-md z-10 mt-1">
                          {sale.sale_courses.slice(1).map((sc, index) => (
                            <div key={index} className="text-sm py-1">
                              {sc.courses.name} - {formatCurrency(Number(sc.price))}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    {formatCurrency(Number(sale.price))}
                    {sale.discount > 0 && (
                      <div className="text-xs text-green-600">
                        Réduction: {sale.discount}% (Prix original: {formatCurrency(Number(sale.original_price))})
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(sale)}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <EditSaleDialog
                        sale={sale}
                        onSaleUpdate={handleUpdateSale}
                        telemarketers={activeTelemarketers}
                        courses={validCourses}
                        customers={validCustomers}
                      />
                      {sale.status !== "paid" && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateSaleStatus2Promise(sale.id)}
                        >
                          Convertir en promesse
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
              {sales.filter(sale => sale.status === "paid" || sale.status === "advance").length === 0 && (
                <TableRow>
                  <TableCell colSpan={7} className="text-center">Aucune vente trouvée</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Détails des ventes</CardTitle>
          <div className="flex space-x-2">
            <Select
              value={selectedTelemarketerId || "all"}
              onValueChange={(value) => setSelectedTelemarketerId(value !== "all" ? value : null)}
            >
              <SelectTrigger className="w-60">
                <SelectValue placeholder="Tous les télévendeurs" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les télévendeurs</SelectItem>
                {activeTelemarketers.map(t => (
                  <SelectItem key={t.id} value={t.id}>{t.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Client</TableHead>
                <TableHead>Formation</TableHead>
                <TableHead>Prix</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredDetailSales.map((sale) => (
                <TableRow key={sale.id}>
                  <TableCell>{new Date(sale.date).toLocaleDateString("fr-FR")}</TableCell>
                  <TableCell>{sale.customers.name}</TableCell>
                  <TableCell>
                    {sale.courses.name}
                    {sale.sale_courses && sale.sale_courses.length > 1 && (
                      <div className="text-xs text-muted-foreground mt-1 cursor-help relative group">
                        + {sale.sale_courses.length - 1} autre(s) formation(s)
                        <div className="hidden group-hover:block absolute bg-popover p-2 rounded-md shadow-md z-10 mt-1">
                          {sale.sale_courses.slice(1).map((sc, index) => (
                            <div key={index} className="text-sm py-1">
                              {sc.courses.name} - {formatCurrency(Number(sc.price))}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    {formatCurrency(Number(sale.price))}
                    {sale.discount > 0 && (
                      <div className="text-xs text-green-600">
                        Réduction: {sale.discount}% (Prix original: {formatCurrency(Number(sale.original_price))})
                      </div>
                    )}
                  </TableCell>
                  <TableCell>{getStatusBadge(sale)}</TableCell>
                  <TableCell>
                    <EditSaleDialog
                      sale={sale}
                      onSaleUpdate={handleUpdateSale}
                      telemarketers={activeTelemarketers}
                      courses={validCourses}
                      customers={validCustomers}
                    />
                  </TableCell>
                </TableRow>
              ))}
              {filteredDetailSales.length === 0 && (
                <TableRow>
                  <TableCell colSpan={6} className="text-center italic">Aucune vente trouvée</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
