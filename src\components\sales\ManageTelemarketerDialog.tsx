
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Telemarketer, useSupabaseTelemarketers } from '@/hooks/useSupabaseTelemarketers';
import { useEffect, useState } from 'react';
import { ComboboxSelect } from './ComboboxSelect';

interface ManageTelemarketerDialogProps {
  telemarketer?: Telemarketer;
  onUpdate: (updated: Telemarketer) => void;
  onArchive: (id: string) => void;
}

export function ManageTelemarketerDialog({
  telemarketer,
  onUpdate,
  onArchive
}: ManageTelemarketerDialogProps) {
  const { telemarketers, isLoading } = useSupabaseTelemarketers();
  const [isOpen, setIsOpen] = useState(false);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [teamLeaderId, setTeamLeaderId] = useState<string | null>(null);

  // Initialize telemarketer data whenever the component mounts or telemarketer prop changes
  useEffect(() => {
    if (telemarketer) {
      setName(telemarketer.name || "");
      setEmail(telemarketer.email || "");
      setPhone(telemarketer.phone || "");
      setTeamLeaderId(telemarketer.team_leader_id || null);
    } else {
      setName("");
      setEmail("");
      setPhone("");
      setTeamLeaderId(null);
    }
  }, [telemarketer]);

  const handleUpdate = () => {
    if (telemarketer && telemarketer.id && name.trim()) {
      onUpdate({
        ...telemarketer,
        name: name.trim(),
        email: email.trim() || null,
        phone: phone.trim() || null,
        team_leader_id: teamLeaderId
      });
      setIsOpen(false);
    }
  };

  const handleArchive = () => {
    if (telemarketer && telemarketer.id) {
      onArchive(telemarketer.id);
      setIsOpen(false);
    }
  };

  // Safe display name
  const displayName = telemarketer?.name || 'Télévendeur';
  const telemarketerExists = Boolean(telemarketer?.id);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">{displayName}</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Modifier le Télévendeur</DialogTitle>
          <DialogDescription>
            Modifiez les informations du télévendeur ou archivez-le.
          </DialogDescription>
        </DialogHeader>
        {telemarketerExists ? (
          <>
            <div className="grid gap-4 py-2">
              <div className="grid gap-2">
                <Label htmlFor="tele-name">Nom</Label>
                <Input
                  id="tele-name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Nom du télévendeur"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="tele-email">Email</Label>
                <Input
                  id="tele-email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Email du télévendeur"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="tele-phone">Téléphone</Label>
                <Input
                  id="tele-phone"
                  type="tel"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  placeholder="Téléphone du télévendeur"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="team-leader">Chef d'équipe</Label>
                {isLoading ? (
                  <div className="text-sm text-muted-foreground">Chargement des télévendeurs...</div>
                ) : (
                  <ComboboxSelect
                    options={telemarketers
                      .filter(t => !t.archived && t.id !== telemarketer?.id)
                      .map(t => ({ label: t.name, value: t.id }))}
                    value={teamLeaderId || ""}
                    onValueChange={(value) => setTeamLeaderId(value || null)}
                    placeholder="Sélectionner un chef d'équipe (optionnel)"
                    searchPlaceholder="Rechercher un télévendeur..."
                    emptyMessage="Aucun télévendeur disponible"
                  />
                )}
              </div>
            </div>
            <DialogFooter className="flex justify-between">
              <Button variant="destructive" onClick={handleArchive}>Archiver</Button>
              <Button onClick={handleUpdate}>Mettre à jour</Button>
            </DialogFooter>
          </>
        ) : (
          <div className="text-center py-4">
            <p>Information du télévendeur non disponible.</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
