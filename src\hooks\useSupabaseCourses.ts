
import { supabase } from "@/integrations/supabase/client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export function useSupabaseCourses() {
  const queryClient = useQueryClient();

  const { data: courses, isLoading } = useQuery({
    queryKey: ['courses'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('courses')
        .select('*')
        .order('name');

      if (error) throw error;
      return data;
    }
  });

  const addCourse = useMutation({
    mutationFn: async (course: { name: string; price: number }) => {
      const { data, error } = await supabase
        .from('courses')
        .insert(course)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['courses'] });
    }
  });

  const updateCourse = useMutation({
    mutationFn: async (course: { id: string; name: string; price: number }) => {
      const { data, error } = await supabase
        .from('courses')
        .update({ name: course.name, price: course.price })
        .eq('id', course.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['courses'] });
    }
  });

  return {
    courses: courses || [],
    isLoading,
    addCourse: addCourse.mutate,
    updateCourse: updateCourse.mutate
  };
}
