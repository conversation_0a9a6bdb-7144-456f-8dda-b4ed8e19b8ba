
import * as React from "react";
import { SearchableSelect } from "@/components/ui/searchable-select";

interface ComboboxSelectProps {
  options: { label: string; value: string }[];
  value: string;
  onValueChange: (value: string) => void;
  placeholder: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  className?: string;
}

export function ComboboxSelect({
  options = [], 
  value = "",  
  onValueChange,
  placeholder = "Sélectionner une option...",
  searchPlaceholder = "Rechercher...",
  emptyMessage = "Aucun résultat trouvé.",
  className,
}: ComboboxSelectProps) {
  // Triple-ensure options is always a valid array of valid objects
  const safeOptions = React.useMemo(() => {
    // First ensure options is an array
    if (!Array.isArray(options)) {
      console.warn("ComboboxSelect: options is not an array", options);
      return [];
    }
    
    // Then ensure each option has valid label/value properties
    const filteredOptions = options.filter(option => 
      option && 
      typeof option === 'object' && 
      'label' in option &&
      'value' in option &&
      typeof option.label === 'string' && 
      typeof option.value === 'string'
    );
    
    if (filteredOptions.length !== options.length) {
      console.warn("ComboboxSelect: Some options were filtered out due to invalid format", options);
    }
    
    return filteredOptions;
  }, [options]);

  // Wrap onValueChange for additional safety
  const handleValueChange = React.useCallback((newValue: string) => {
    if (typeof onValueChange === 'function') {
      onValueChange(newValue);
    } else {
      console.warn("ComboboxSelect: onValueChange is not a function");
    }
  }, [onValueChange]);

  return (
    <SearchableSelect
      options={safeOptions}
      value={value}
      onValueChange={handleValueChange}
      placeholder={placeholder}
      searchPlaceholder={searchPlaceholder}
      emptyMessage={emptyMessage}
      className={className}
    />
  );
}
