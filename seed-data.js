// Script pour remplir la base de données avec des données de test
import { createClient } from '@supabase/supabase-js';

// Supabase connection details
const SUPABASE_URL = "https://vxzpeinztvurtncaaqdm.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ4enBlaW56dHZ1cnRuY2FhcWRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0ODY5MzksImV4cCI6MjA2MTA2MjkzOX0.EMJQUlI3yEwGzHCzuJcs8phfrEVDzJkavIdyKfbbk9s";

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// Données de test
const teamLeaders = [
  { name: "<PERSON>", email: "<EMAIL>", phone: "237612345678" },
  { name: "<PERSON>", email: "<EMAIL>", phone: "237623456789" }
];

const telemarketers = [
  { name: "<PERSON>", email: "<EMAIL>", phone: "237634567890" },
  { name: "<PERSON>", email: "<EMAIL>", phone: "237645678901" },
  { name: "Thomas Petit", email: "<EMAIL>", phone: "237656789012" },
  { name: "Julie Leroy", email: "<EMAIL>", phone: "237667890123" },
  { name: "Nicolas Moreau", email: "<EMAIL>", phone: "237678901234" }
];

const courses = [
  { name: "Formation Excel Avancé", price: 75000 },
  { name: "Formation Word Professionnel", price: 65000 },
  { name: "Formation PowerPoint", price: 60000 },
  { name: "Formation Développement Web", price: 150000 },
  { name: "Formation Marketing Digital", price: 120000 }
];

const customers = [
  { name: "Entreprise ABC", email: "<EMAIL>", phone: "237689012345" },
  { name: "Société XYZ", email: "<EMAIL>", phone: "237690123456" },
  { name: "Boutique 123", email: "<EMAIL>", phone: "237601234567" },
  { name: "Restaurant Le Gourmet", email: "<EMAIL>", phone: "237612345678" },
  { name: "Hôtel Luxe", email: "<EMAIL>", phone: "237623456789" },
  { name: "Cabinet Juridique", email: "<EMAIL>", phone: "237634567890" },
  { name: "École Privée", email: "<EMAIL>", phone: "237645678901" },
  { name: "Clinique Santé", email: "<EMAIL>", phone: "237656789012" },
  { name: "Garage Auto", email: "<EMAIL>", phone: "237667890123" },
  { name: "Boulangerie Pain Doré", email: "<EMAIL>", phone: "237678901234" }
];

// Fonction pour générer une date aléatoire dans les 30 derniers jours
function getRandomDate(daysBack = 30) {
  const today = new Date();
  const pastDate = new Date(today);
  pastDate.setDate(today.getDate() - Math.floor(Math.random() * daysBack));
  return pastDate.toISOString().split('T')[0];
}

// Fonction pour générer une date future aléatoire dans les 30 prochains jours
function getRandomFutureDate(daysAhead = 30) {
  const today = new Date();
  const futureDate = new Date(today);
  futureDate.setDate(today.getDate() + Math.floor(Math.random() * daysAhead) + 1);
  return futureDate.toISOString().split('T')[0];
}

// Fonction pour générer un nombre aléatoire entre min et max
function getRandomNumber(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Fonction pour insérer les données dans la base de données
async function seedDatabase() {
  console.log("Début de l'insertion des données...");

  // Insérer les team leaders
  console.log("Insertion des team leaders...");
  const { data: insertedTeamLeaders, error: teamLeadersError } = await supabase
    .from('telemarketers')
    .insert(teamLeaders)
    .select();

  if (teamLeadersError) {
    console.error("Erreur lors de l'insertion des team leaders:", teamLeadersError);
    return;
  }

  console.log(`${insertedTeamLeaders.length} team leaders insérés.`);

  // Nous n'avons pas besoin de mettre à jour les télévendeurs avec les team leaders
  // car la colonne team_leader_id n'existe pas dans la table telemarketers

  // Insérer les télévendeurs
  console.log("Insertion des télévendeurs...");
  const { data: insertedTelemarketers, error: telemarketerError } = await supabase
    .from('telemarketers')
    .insert(telemarketers)
    .select();

  if (telemarketerError) {
    console.error("Erreur lors de l'insertion des télévendeurs:", telemarketerError);
    return;
  }

  console.log(`${insertedTelemarketers.length} télévendeurs insérés.`);

  // Insérer les formations
  console.log("Insertion des formations...");
  const { data: insertedCourses, error: coursesError } = await supabase
    .from('courses')
    .insert(courses)
    .select();

  if (coursesError) {
    console.error("Erreur lors de l'insertion des formations:", coursesError);
    return;
  }

  console.log(`${insertedCourses.length} formations insérées.`);

  // Insérer les clients
  console.log("Insertion des clients...");
  const { data: insertedCustomers, error: customersError } = await supabase
    .from('customers')
    .insert(customers)
    .select();

  if (customersError) {
    console.error("Erreur lors de l'insertion des clients:", customersError);
    return;
  }

  console.log(`${insertedCustomers.length} clients insérés.`);

  // Générer et insérer des ventes
  const sales = [];
  const statuses = ['paid', 'advance', 'promise'];
  const allTelemarketers = [...insertedTeamLeaders, ...insertedTelemarketers];

  // Générer 30 ventes
  for (let i = 0; i < 30; i++) {
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const course = insertedCourses[Math.floor(Math.random() * insertedCourses.length)];
    const price = course.price;

    const sale = {
      customer_id: insertedCustomers[Math.floor(Math.random() * insertedCustomers.length)].id,
      telemarketer_id: allTelemarketers[Math.floor(Math.random() * allTelemarketers.length)].id,
      course_id: course.id,
      date: getRandomDate(),
      price: price,
      status: status,
      due_date: getRandomFutureDate() // Toutes les ventes ont besoin d'une date d'échéance
    };

    sales.push(sale);
  }

  // Insérer les ventes
  console.log("Insertion des ventes...");
  const { data: insertedSales, error: salesError } = await supabase
    .from('sales')
    .insert(sales)
    .select();

  if (salesError) {
    console.error("Erreur lors de l'insertion des ventes:", salesError);
    return;
  }

  console.log(`${insertedSales.length} ventes insérées.`);

  console.log("Insertion des données terminée avec succès!");
}

// Exécuter la fonction d'insertion
seedDatabase();
