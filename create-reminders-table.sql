-- Script SQL pour créer la table reminders manquante dans la base de données Supabase

-- Activer l'extension uuid-ossp si elle n'est pas déjà activée
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Vérifier si le type reminder_status existe déjà
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'reminder_status') THEN
    CREATE TYPE reminder_status AS ENUM ('pending', 'completed', 'cancelled');
  END IF;
END
$$;

-- Créer la table reminders si elle n'existe pas
CREATE TABLE IF NOT EXISTS reminders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID NOT NULL REFERENCES customers(id),
  telemarketer_id UUID NOT NULL REFERENCES telemarketers(id),
  course_id UUID NOT NULL REFERENCES courses(id),
  amount NUMERIC NOT NULL,
  due_date DATE NOT NULL,
  status reminder_status NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ajouter des index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_reminders_customer_id ON reminders(customer_id);
CREATE INDEX IF NOT EXISTS idx_reminders_telemarketer_id ON reminders(telemarketer_id);
CREATE INDEX IF NOT EXISTS idx_reminders_course_id ON reminders(course_id);
CREATE INDEX IF NOT EXISTS idx_reminders_status ON reminders(status);
CREATE INDEX IF NOT EXISTS idx_reminders_due_date ON reminders(due_date);

-- Afficher un message de confirmation
DO $$
BEGIN
  RAISE NOTICE 'Table reminders créée avec succès avec tous les index nécessaires';
END
$$;
