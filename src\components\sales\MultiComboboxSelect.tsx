import * as React from "react";
import { MultiSearchableSelect } from "@/components/ui/multi-searchable-select";

interface MultiComboboxSelectProps {
  options: { label: string; value: string }[];
  values: string[];
  onValuesChange: (values: string[]) => void;
  placeholder: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  className?: string;
}

export function MultiComboboxSelect({
  options = [], 
  values = [],  
  onValuesChange,
  placeholder = "Sélectionner des options...",
  searchPlaceholder = "Rechercher...",
  emptyMessage = "Aucun résultat trouvé.",
  className,
}: MultiComboboxSelectProps) {
  // Triple-ensure options is always a valid array of valid objects
  const safeOptions = React.useMemo(() => {
    // First ensure options is an array
    if (!Array.isArray(options)) {
      console.warn("MultiComboboxSelect: options is not an array", options);
      return [];
    }
    
    // Then ensure each option has valid label/value properties
    const filteredOptions = options.filter(option => 
      option && 
      typeof option === 'object' && 
      'label' in option &&
      'value' in option &&
      typeof option.label === 'string' && 
      typeof option.value === 'string'
    );
    
    if (filteredOptions.length !== options.length) {
      console.warn("MultiComboboxSelect: Some options were filtered out due to invalid format", options);
    }
    
    return filteredOptions;
  }, [options]);

  // Ensure values is always a valid array of strings
  const safeValues = React.useMemo(() => {
    if (!Array.isArray(values)) {
      console.warn("MultiComboboxSelect: values is not an array", values);
      return [];
    }
    
    return values.filter(value => typeof value === 'string');
  }, [values]);

  // Wrap onValuesChange for additional safety
  const handleValuesChange = React.useCallback((newValues: string[]) => {
    if (typeof onValuesChange === 'function') {
      onValuesChange(newValues);
    } else {
      console.warn("MultiComboboxSelect: onValuesChange is not a function");
    }
  }, [onValuesChange]);

  return (
    <MultiSearchableSelect
      options={safeOptions}
      values={safeValues}
      onValuesChange={handleValuesChange}
      placeholder={placeholder}
      searchPlaceholder={searchPlaceholder}
      emptyMessage={emptyMessage}
      className={className}
    />
  );
}
