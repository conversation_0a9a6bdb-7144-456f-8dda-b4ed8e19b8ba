
import { ComboboxSelect } from "@/components/sales/ComboboxSelect";
import { ManageTelemarketerDialog } from "@/components/sales/ManageTelemarketerDialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useSupabaseTelemarketers } from "@/hooks/useSupabaseTelemarketers";
import { useState } from "react";
import { toast } from "sonner";

export default function Telemarketers() {
  const { telemarketers, updateTelemarketer, archiveTelemarketer, unarchiveTelemarketer, addTelemarketer, isLoading } = useSupabaseTelemarketers();
  const [newTelename, setNewTelename] = useState("");
  const [newTeleEmail, setNewTeleEmail] = useState("");
  const [newTelePhone, setNewTelePhone] = useState("");
  const [newTeamLeaderId, setNewTeamLeaderId] = useState<string | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  const activeTelemarketers = telemarketers.filter(t => !t.archived);
  const archivedTelemarketers = telemarketers.filter(t => t.archived);

  const handleAddTelemarketer = () => {
    if (!newTelename.trim()) {
      toast.error("Le nom du télévendeur est requis");
      return;
    }

    addTelemarketer(
      {
        name: newTelename.trim(),
        email: newTeleEmail.trim() || null,
        phone: newTelePhone.trim() || null,
        team_leader_id: newTeamLeaderId
      },
      {
        onSuccess: () => {
          toast.success("Télévendeur ajouté avec succès");
          setNewTelename("");
          setNewTeleEmail("");
          setNewTelePhone("");
          setNewTeamLeaderId(null);
          setIsAddDialogOpen(false);
        },
        onError: (error) => {
          toast.error("Erreur lors de l'ajout du télévendeur");
          console.error("Error adding telemarketer:", error);
        }
      }
    );
  };

  const handleUnarchiveTelemarketer = (id: string) => {
    unarchiveTelemarketer(
      id,
      {
        onSuccess: () => {
          toast.success("Télévendeur désarchivé avec succès");
        },
        onError: (error) => {
          toast.error("Erreur lors de la désarchivation du télévendeur");
          console.error("Error unarchiving telemarketer:", error);
        }
      }
    );
  };

  const handleUpdateTelemarketer = (telemarketer: any) => {
    updateTelemarketer(
      telemarketer,
      {
        onSuccess: () => {
          toast.success("Télévendeur mis à jour avec succès");
        },
        onError: (error) => {
          toast.error("Erreur lors de la mise à jour du télévendeur");
          console.error("Error updating telemarketer:", error);
        }
      }
    );
  };

  const handleArchiveTelemarketer = (id: string) => {
    archiveTelemarketer(
      id,
      {
        onSuccess: () => {
          toast.success("Télévendeur archivé avec succès");
        },
        onError: (error) => {
          toast.error("Erreur lors de l'archivage du télévendeur");
          console.error("Error archiving telemarketer:", error);
        }
      }
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Télévendeurs</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>Ajouter un télévendeur</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Ajouter un nouveau télévendeur</DialogTitle>
              <DialogDescription>
                Entrez les informations du nouveau télévendeur
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Nom
                </Label>
                <Input
                  id="name"
                  value={newTelename}
                  onChange={(e) => setNewTelename(e.target.value)}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="email" className="text-right">
                  Email
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={newTeleEmail}
                  onChange={(e) => setNewTeleEmail(e.target.value)}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="phone" className="text-right">
                  Téléphone
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  value={newTelePhone}
                  onChange={(e) => setNewTelePhone(e.target.value)}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="team-leader" className="text-right">
                  Chef d'équipe
                </Label>
                <div className="col-span-3">
                  {isLoading ? (
                    <div className="text-sm text-muted-foreground">Chargement des télévendeurs...</div>
                  ) : (
                    <ComboboxSelect
                      options={telemarketers
                        .filter(t => !t.archived)
                        .map(t => ({ label: t.name, value: t.id }))}
                      value={newTeamLeaderId || ""}
                      onValueChange={(value) => setNewTeamLeaderId(value || null)}
                      placeholder="Sélectionner un chef d'équipe (optionnel)"
                      searchPlaceholder="Rechercher un télévendeur..."
                      emptyMessage="Aucun télévendeur disponible"
                    />
                  )}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={handleAddTelemarketer}>Ajouter</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Télévendeurs Actifs</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nom</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Téléphone</TableHead>
                <TableHead>Chef d'équipe</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {activeTelemarketers.map((telemarketer) => (
                <TableRow key={telemarketer.id}>
                  <TableCell>{telemarketer.name}</TableCell>
                  <TableCell>{telemarketer.email || "-"}</TableCell>
                  <TableCell>{telemarketer.phone || "-"}</TableCell>
                  <TableCell>{telemarketer.team_leader?.name || "-"}</TableCell>
                  <TableCell>
                    <ManageTelemarketerDialog
                      telemarketer={telemarketer}
                      onUpdate={(updated) => handleUpdateTelemarketer(updated)}
                      onArchive={(id) => handleArchiveTelemarketer(id)}
                    />
                  </TableCell>
                </TableRow>
              ))}
              {activeTelemarketers.length === 0 && (
                <TableRow>
                  <TableCell colSpan={6} className="text-center italic">
                    Aucun télévendeur actif trouvé
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {archivedTelemarketers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Télévendeurs Archivés</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nom</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Téléphone</TableHead>
                  <TableHead>Chef d'équipe</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {archivedTelemarketers.map((telemarketer) => (
                  <TableRow key={telemarketer.id} className="text-muted-foreground">
                    <TableCell>{telemarketer.name}</TableCell>
                    <TableCell>{telemarketer.email || "-"}</TableCell>
                    <TableCell>{telemarketer.phone || "-"}</TableCell>
                    <TableCell>{telemarketer.team_leader?.name || "-"}</TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleUnarchiveTelemarketer(telemarketer.id)}
                      >
                        Désarchiver
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
