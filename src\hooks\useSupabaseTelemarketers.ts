
import { supabase } from "@/integrations/supabase/client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export type Telemarketer = {
  id: string;
  name: string;
  email?: string | null;
  phone?: string | null;
  archived?: boolean;
  team_leader_id?: string | null;
  team_leader?: Telemarketer | null;
}

export function useSupabaseTelemarketers() {
  const queryClient = useQueryClient();

  const { data: telemarketers, isLoading } = useQuery({
    queryKey: ['telemarketers'],
    queryFn: async () => {
      // First, get all telemarketers
      const { data, error } = await supabase
        .from('telemarketers')
        .select('*')
        .order('name');

      if (error) {
        console.error("Erreur lors de la récupération des télévendeurs:", error);
        throw error;
      }

      // Process the data to add team leader information manually
      const processedData = await Promise.all(data.map(async (telemarketer) => {
        // Vérifier si la propriété team_leader_id existe dans l'objet telemarketer
        if (telemarketer.hasOwnProperty('team_leader_id') && telemarketer.team_leader_id) {
          try {
            // Fetch the team leader information
            const { data: teamLeaderData, error: teamLeaderError } = await supabase
              .from('telemarketers')
              .select('id, name, email, phone')
              .eq('id', telemarketer.team_leader_id)
              .single();

            if (!teamLeaderError && teamLeaderData) {
              return {
                ...telemarketer,
                team_leader: teamLeaderData
              };
            }
          } catch (error) {
            console.warn("Erreur lors de la récupération du chef d'équipe:", error);
          }
        }
        return {
          ...telemarketer,
          team_leader_id: telemarketer.team_leader_id || null,
          team_leader: null
        };
      }));

      console.log("Télévendeurs récupérés:", processedData);
      return processedData;
    }
  });

  const addTelemarketer = useMutation({
    mutationFn: async (telemarketer: {
      name: string;
      email?: string | null;
      phone?: string | null;
      team_leader_id?: string | null;
    }) => {
      // Créer un objet sans team_leader_id pour éviter l'erreur si la colonne n'existe pas
      const { team_leader_id, ...telemarketerWithoutTeamLeader } = telemarketer;

      try {
        // Essayer d'abord d'insérer avec team_leader_id
        const { data, error } = await supabase
          .from('telemarketers')
          .insert(telemarketer)
          .select('*')
          .single();

        if (error) {
          // Si l'erreur concerne la colonne team_leader_id, réessayer sans cette colonne
          if (error.message && error.message.includes('team_leader_id')) {
            console.warn("La colonne team_leader_id n'existe pas encore. Insertion sans cette colonne.");
            const { data: dataWithoutTeamLeader, error: errorWithoutTeamLeader } = await supabase
              .from('telemarketers')
              .insert(telemarketerWithoutTeamLeader)
              .select('*')
              .single();

            if (errorWithoutTeamLeader) throw errorWithoutTeamLeader;
            return {
              ...dataWithoutTeamLeader,
              team_leader_id: null,
              team_leader: null
            };
          } else {
            throw error;
          }
        }

        if (!data) throw new Error("Aucune donnée retournée après l'insertion");

        // If there's a team leader, fetch their information
        if (data.team_leader_id) {
          const { data: teamLeaderData, error: teamLeaderError } = await supabase
            .from('telemarketers')
            .select('id, name, email, phone')
            .eq('id', data.team_leader_id)
            .single();

          if (!teamLeaderError && teamLeaderData) {
            return {
              ...data,
              team_leader: teamLeaderData
            };
          }
        }

        return {
          ...data,
          team_leader: null
        };
      } catch (error) {
        console.error("Erreur lors de l'ajout du télévendeur:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['telemarketers'] });
    }
  });

  const updateTelemarketer = useMutation({
    mutationFn: async (telemarketerData: any) => {
      console.log("Updating telemarketer with data:", telemarketerData);

      // Extraire l'ID et les autres propriétés
      const { id, team_leader, ...updateData } = telemarketerData;

      if (!id) {
        throw new Error("ID du télévendeur manquant");
      }

      // Créer un objet de mise à jour propre
      const cleanUpdateData = {
        name: updateData.name,
        email: updateData.email || null,
        phone: updateData.phone || null,
        team_leader_id: updateData.team_leader_id || null
      };

      console.log("Clean update data:", cleanUpdateData);

      try {
        // Mettre à jour le télévendeur
        const { data, error } = await supabase
          .from('telemarketers')
          .update(cleanUpdateData)
          .eq('id', id)
          .select('*')
          .single();

        if (error) {
          console.error("Supabase error during update:", error);
          throw error;
        }

        if (!data) {
          throw new Error("Aucune donnée retournée après la mise à jour");
        }

        // Si le télévendeur a un chef d'équipe, récupérer ses informations
        if (data.team_leader_id) {
          const { data: teamLeaderData, error: teamLeaderError } = await supabase
            .from('telemarketers')
            .select('id, name, email, phone')
            .eq('id', data.team_leader_id)
            .single();

          if (!teamLeaderError && teamLeaderData) {
            return {
              ...data,
              team_leader: teamLeaderData
            };
          }
        }

        return {
          ...data,
          team_leader: null
        };
      } catch (error) {
        console.error("Erreur lors de la mise à jour du télévendeur:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['telemarketers'] });
    }
  });

  const archiveTelemarketer = useMutation({
    mutationFn: async (id: string) => {
      // Archive the telemarketer
      const { data, error } = await supabase
        .from('telemarketers')
        .update({ archived: true })
        .eq('id', id)
        .select('*')
        .single();

      if (error) throw error;

      if (!data) throw new Error("Aucune donnée retournée après l'archivage");

      // If there's a team leader, fetch their information
      if (data.team_leader_id) {
        const { data: teamLeaderData, error: teamLeaderError } = await supabase
          .from('telemarketers')
          .select('id, name, email, phone')
          .eq('id', data.team_leader_id)
          .single();

        if (!teamLeaderError && teamLeaderData) {
          return {
            ...data,
            team_leader: teamLeaderData
          };
        }
      }

      return {
        ...data,
        team_leader: null
      };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['telemarketers'] });
    }
  });

  const unarchiveTelemarketer = useMutation({
    mutationFn: async (id: string) => {
      // Unarchive the telemarketer
      const { data, error } = await supabase
        .from('telemarketers')
        .update({ archived: false })
        .eq('id', id)
        .select('*')
        .single();

      if (error) throw error;

      if (!data) throw new Error("Aucune donnée retournée après la désarchivation");

      // If there's a team leader, fetch their information
      if (data.team_leader_id) {
        const { data: teamLeaderData, error: teamLeaderError } = await supabase
          .from('telemarketers')
          .select('id, name, email, phone')
          .eq('id', data.team_leader_id)
          .single();

        if (!teamLeaderError && teamLeaderData) {
          return {
            ...data,
            team_leader: teamLeaderData
          };
        }
      }

      return {
        ...data,
        team_leader: null
      };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['telemarketers'] });
    }
  });

  return {
    telemarketers: telemarketers || [],
    isLoading,
    addTelemarketer: addTelemarketer.mutate,
    updateTelemarketer: updateTelemarketer.mutate,
    archiveTelemarketer: archiveTelemarketer.mutate,
    unarchiveTelemarketer: unarchiveTelemarketer.mutate
  };
}
