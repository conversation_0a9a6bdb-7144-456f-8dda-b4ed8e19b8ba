
import * as React from "react";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";

export interface SearchableSelectOption {
  label: string;
  value: string;
}

interface SearchableSelectProps {
  options: SearchableSelectOption[];
  value: string;
  onValueChange: (value: string) => void;
  placeholder: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  className?: string;
}

export function SearchableSelect({
  options = [],
  value = "",
  onValueChange,
  placeholder = "Select an option...",
  searchPlaceholder = "Search...",
  emptyMessage = "No results found.",
  className,
}: SearchableSelectProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");

  // Enhanced validation to ensure options is always a valid array with valid items
  const validOptions = React.useMemo(() => {
    if (!options) return [];
    if (!Array.isArray(options)) {
      console.warn("SearchableSelect: options is not an array", options);
      return [];
    }
    
    return options.filter(option => 
      option && 
      typeof option === 'object' && 
      'label' in option && 
      'value' in option &&
      typeof option.label === 'string' && 
      typeof option.value === 'string'
    );
  }, [options]);

  // Find the selected option label with improved null checking
  const selectedLabel = React.useMemo(() => {
    if (!value) return placeholder;
    const selected = validOptions.find(option => option.value === value);
    return selected?.label || placeholder;
  }, [value, validOptions, placeholder]);

  // Filter options based on search query with improved validation
  const filteredOptions = React.useMemo(() => {
    if (!searchQuery || !searchQuery.trim()) return validOptions;
    
    return validOptions.filter(option => 
      option.label.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [validOptions, searchQuery]);

  // Wrap onValueChange to ensure we close the popover and reset search
  const handleSelect = React.useCallback((selectedValue: string) => {
    if (typeof onValueChange === 'function') {
      onValueChange(selectedValue);
    }
    setSearchQuery("");
    setOpen(false);
  }, [onValueChange]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between", className)}
          type="button" // Add explicit type to prevent form submission
        >
          <span className="truncate">{selectedLabel}</span>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <div className="flex flex-col">
          <div className="flex items-center border-b px-3">
            <Input
              placeholder={searchPlaceholder}
              className="h-9 border-none focus-visible:ring-0 focus-visible:ring-offset-0"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="max-h-[200px] overflow-auto p-1">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option) => (
                <div
                  key={option.value}
                  className={cn(
                    "relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground",
                    value === option.value && "bg-accent text-accent-foreground"
                  )}
                  onClick={() => handleSelect(option.value)}
                >
                  {option.label}
                  {value === option.value && (
                    <Check className="ml-auto h-4 w-4" />
                  )}
                </div>
              ))
            ) : (
              <div className="py-6 text-center text-sm">{searchQuery ? emptyMessage : "Commencer à taper pour rechercher..."}</div>
            )}
            {validOptions.length === 0 && (
              <div className="py-6 text-center text-sm">Aucune option disponible</div>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
