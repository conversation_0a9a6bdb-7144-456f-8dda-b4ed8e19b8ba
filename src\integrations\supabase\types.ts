export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      advance_payments: {
        Row: {
          amount: number
          created_at: string
          id: string
          payment_date: string
          sale_id: string
        }
        Insert: {
          amount: number
          created_at?: string
          id?: string
          payment_date: string
          sale_id: string
        }
        Update: {
          amount?: number
          created_at?: string
          id?: string
          payment_date?: string
          sale_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "advance_payments_sale_id_fkey"
            columns: ["sale_id"]
            isOneToOne: false
            referencedRelation: "sales"
            referencedColumns: ["id"]
          },
        ]
      }
      courses: {
        Row: {
          created_at: string
          id: string
          name: string
          price: number
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          price: number
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          price?: number
        }
        Relationships: []
      }
      customers: {
        Row: {
          created_at: string
          email: string | null
          id: string
          name: string
          phone: string | null
        }
        Insert: {
          created_at?: string
          email?: string | null
          id?: string
          name: string
          phone?: string | null
        }
        Update: {
          created_at?: string
          email?: string | null
          id?: string
          name?: string
          phone?: string | null
        }
        Relationships: []
      }
      promises: {
        Row: {
          amount: number
          course_id: string
          created_at: string
          customer_id: string
          due_date: string
          id: string
          original_due_date: string
          reminders: number
          status: Database["public"]["Enums"]["promise_status"]
          telemarketer_id: string
        }
        Insert: {
          amount: number
          course_id: string
          created_at?: string
          customer_id: string
          due_date: string
          id?: string
          original_due_date: string
          reminders?: number
          status?: Database["public"]["Enums"]["promise_status"]
          telemarketer_id: string
        }
        Update: {
          amount?: number
          course_id?: string
          created_at?: string
          customer_id?: string
          due_date?: string
          id?: string
          original_due_date?: string
          reminders?: number
          status?: Database["public"]["Enums"]["promise_status"]
          telemarketer_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "promises_course_id_fkey"
            columns: ["course_id"]
            isOneToOne: false
            referencedRelation: "courses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "promises_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "promises_telemarketer_id_fkey"
            columns: ["telemarketer_id"]
            isOneToOne: false
            referencedRelation: "telemarketers"
            referencedColumns: ["id"]
          },
        ]
      }
      reminders: {
        Row: {
          amount: number
          course_id: string
          created_at: string
          customer_id: string
          due_date: string
          id: string
          status: Database["public"]["Enums"]["reminder_status"]
          telemarketer_id: string
        }
        Insert: {
          amount: number
          course_id: string
          created_at?: string
          customer_id: string
          due_date: string
          id?: string
          status?: Database["public"]["Enums"]["reminder_status"]
          telemarketer_id: string
        }
        Update: {
          amount?: number
          course_id?: string
          created_at?: string
          customer_id?: string
          due_date?: string
          id?: string
          status?: Database["public"]["Enums"]["reminder_status"]
          telemarketer_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "reminders_course_id_fkey"
            columns: ["course_id"]
            isOneToOne: false
            referencedRelation: "courses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reminders_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reminders_telemarketer_id_fkey"
            columns: ["telemarketer_id"]
            isOneToOne: false
            referencedRelation: "telemarketers"
            referencedColumns: ["id"]
          },
        ]
      }
      sales: {
        Row: {
          course_id: string
          created_at: string
          customer_id: string
          date: string
          due_date: string
          id: string
          price: number
          status: Database["public"]["Enums"]["sale_status"]
          telemarketer_id: string
        }
        Insert: {
          course_id: string
          created_at?: string
          customer_id: string
          date: string
          due_date: string
          id?: string
          price: number
          status?: Database["public"]["Enums"]["sale_status"]
          telemarketer_id: string
        }
        Update: {
          course_id?: string
          created_at?: string
          customer_id?: string
          date?: string
          due_date?: string
          id?: string
          price?: number
          status?: Database["public"]["Enums"]["sale_status"]
          telemarketer_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "sales_course_id_fkey"
            columns: ["course_id"]
            isOneToOne: false
            referencedRelation: "courses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sales_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sales_telemarketer_id_fkey"
            columns: ["telemarketer_id"]
            isOneToOne: false
            referencedRelation: "telemarketers"
            referencedColumns: ["id"]
          },
        ]
      }
      telemarketers: {
        Row: {
          archived: boolean
          created_at: string
          email: string | null
          id: string
          name: string
          phone: string | null
          team_leader_id: string | null
        }
        Insert: {
          archived?: boolean
          created_at?: string
          email?: string | null
          id?: string
          name: string
          phone?: string | null
          team_leader_id?: string | null
        }
        Update: {
          archived?: boolean
          created_at?: string
          email?: string | null
          id?: string
          name?: string
          phone?: string | null
          team_leader_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "telemarketers_team_leader_id_fkey"
            columns: ["team_leader_id"]
            isOneToOne: false
            referencedRelation: "telemarketers"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      promise_status: "pending" | "postponed" | "fulfilled" | "reminder-sent"
      reminder_status: "pending" | "completed" | "cancelled"
      sale_status: "paid" | "advance" | "promise" | "reminder"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
  | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
  | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
    Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
  : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
    Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
  ? R
  : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
    DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
    DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R
    }
  ? R
  : never
  : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
  | keyof DefaultSchema["Tables"]
  | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
  : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
    Insert: infer I
  }
  ? I
  : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
    Insert: infer I
  }
  ? I
  : never
  : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
  | keyof DefaultSchema["Tables"]
  | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
  : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
    Update: infer U
  }
  ? U
  : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
    Update: infer U
  }
  ? U
  : never
  : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
  | keyof DefaultSchema["Enums"]
  | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
  : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
  | keyof DefaultSchema["CompositeTypes"]
  | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
  : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never

export const Constants = {
  public: {
    Enums: {
      promise_status: ["pending", "postponed", "fulfilled", "reminder-sent"],
      reminder_status: ["pending", "completed", "cancelled"],
      sale_status: ["paid", "advance", "promise", "reminder"],
    },
  },
} as const
