import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export type Promise = Database["public"]["Tables"]["promises"]["Row"];
export type Sale = Database["public"]["Tables"]["sales"]["Row"];

// Common interface for both promises and sales with status "promise"
export interface UnifiedPromise {
  id: string;
  customer_id: string;
  telemarketer_id: string;
  course_id: string;
  amount: number;
  due_date: string;
  original_due_date?: string;
  reminders?: number;
  status: string;
  source: 'promise' | 'sale'; // To track the source of the promise
  created_at?: string; // Date de création pour les promesses
  date_created?: string; // Date de création pour les ventes
}

export type PromiseWithRelations = UnifiedPromise & {
  customers: {
    name: string;
    email: string | null;
    phone: string | null;
  };
  telemarketers: {
    name: string;
    email: string | null;
  };
  courses: {
    name: string;
  };
};

export function useSupabasePromises() {
  const queryClient = useQueryClient();

  const { data: promises, isLoading, error: promisesError } = useQuery({
    queryKey: ['promises'],
    queryFn: async () => {
      try {
        // Fetch promises from the promises table
        const { data: promisesData, error: promisesError } = await supabase
          .from('promises')
          .select(`
            *,
            customers:customer_id(*),
            telemarketers:telemarketer_id(*),
            courses:course_id(*)
          `)
          .order('due_date');

        if (promisesError) {
          console.error("Error fetching promises:", promisesError);
          throw promisesError;
        }

        // Fetch sales with status "promise"
        const { data: salesData, error: salesError } = await supabase
          .from('sales')
          .select(`
            *,
            customers:customer_id(*),
            telemarketers:telemarketer_id(*),
            courses:course_id(*)
          `)
          .eq('status', 'promise')
          .order('due_date');

        if (salesError) {
          console.error("Error fetching sales with promise status:", salesError);
          throw salesError;
        }

        // Transform promises data to match the UnifiedPromise interface
        const formattedPromises: PromiseWithRelations[] = promisesData.map(promise => ({
          id: promise.id,
          customer_id: promise.customer_id,
          telemarketer_id: promise.telemarketer_id,
          course_id: promise.course_id,
          amount: promise.amount,
          due_date: promise.due_date,
          original_due_date: promise.original_due_date,
          reminders: promise.reminders,
          status: promise.status,
          source: 'promise',
          created_at: promise.created_at, // Ajout du champ created_at
          customers: promise.customers,
          telemarketers: promise.telemarketers,
          courses: promise.courses
        }));

        // Transform sales data to match the UnifiedPromise interface
        const formattedSales: PromiseWithRelations[] = salesData.map(sale => ({
          id: sale.id,
          customer_id: sale.customer_id,
          telemarketer_id: sale.telemarketer_id,
          course_id: sale.course_id,
          amount: sale.price,
          due_date: sale.due_date,
          original_due_date: undefined,
          reminders: 0,
          status: 'pending', // Default status for sales promises
          source: 'sale',
          date_created: sale.date, // Ajout du champ date_created
          customers: sale.customers,
          telemarketers: sale.telemarketers,
          courses: sale.courses
        }));

        // Combine both arrays
        return [...formattedPromises, ...formattedSales];
      } catch (error) {
        console.error("Error in promises query:", error);
        throw error;
      }
    }
  });

  const addPromise = useMutation({
    mutationFn: async (promise: {
      customer_id: string;
      telemarketer_id: string;
      course_id: string;
      amount: number;
      due_date: string;
    }) => {
      // Make sure to include the required original_due_date field
      const formattedPromise = {
        ...promise,
        original_due_date: promise.due_date, // Set original_due_date to the same value as due_date initially
        reminders: 0, // Adding default value for reminders
        status: 'pending' as Database["public"]["Enums"]["promise_status"]
      };

      const { data, error } = await supabase
        .from('promises')
        .insert(formattedPromise)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['promises'] });
    }
  });

  const updatePromiseStatus = useMutation({
    mutationFn: async ({ id, status, source = 'promise', ...rest }: {
      id: string;
      status: Database["public"]["Enums"]["promise_status"];
      source?: 'promise' | 'sale';
      reminders?: number;
      due_date?: string;
      original_due_date?: string
    }) => {
      if (source === 'promise') {
        // Update in promises table
        const { data, error } = await supabase
          .from('promises')
          .update({ status, ...rest })
          .eq('id', id)
          .select()
          .single();

        if (error) {
          console.error("Error updating promise status:", error);
          throw error;
        }
        return data;
      } else {
        // For sales with status "promise", we need to update in the sales table
        // We only update the due_date for sales, as they don't have the same status enum
        const updateData: any = {};
        if (rest.due_date) {
          updateData.due_date = rest.due_date;
        }

        const { data, error } = await supabase
          .from('sales')
          .update(updateData)
          .eq('id', id)
          .select()
          .single();

        if (error) {
          console.error("Error updating sale promise status:", error);
          throw error;
        }
        return data;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['promises'] });
      queryClient.invalidateQueries({ queryKey: ['sales'] });
    }
  });

  const fulfillPromise = useMutation({
    mutationFn: async ({ id, source = 'promise' }: { id: string; source?: 'promise' | 'sale' }) => {
      if (source === 'promise') {
        // Handle regular promise from promises table
        try {
          // Get the promise details first
          const { data: promise, error: getError } = await supabase
            .from('promises')
            .select('*')
            .eq('id', id)
            .single();

          if (getError) {
            console.error("Error getting promise details:", getError);
            throw getError;
          }

          // Mark the promise as fulfilled
          const { data, error: updateError } = await supabase
            .from('promises')
            .update({ status: 'fulfilled' })
            .eq('id', id)
            .select();

          if (updateError) {
            console.error("Error updating promise status:", updateError);
            throw updateError;
          }

          // Create a new sale record
          const { error: saleError } = await supabase
            .from('sales')
            .insert({
              customer_id: promise.customer_id,
              telemarketer_id: promise.telemarketer_id,
              course_id: promise.course_id,
              price: promise.amount,
              status: 'paid',
              date: new Date().toISOString(),
              due_date: new Date().toISOString()
            });

          if (saleError) {
            console.error("Error creating sale from promise:", saleError);
            throw saleError;
          }

          return data;
        } catch (err) {
          console.error("Error fulfilling promise:", err);
          throw err;
        }
      } else {
        // Handle sale with status "promise"
        try {
          // Update the sale status from "promise" to "paid"
          const { data, error } = await supabase
            .from('sales')
            .update({
              status: 'paid',
              date: new Date().toISOString()
            })
            .eq('id', id)
            .select();

          if (error) {
            console.error("Error updating sale status:", error);
            throw error;
          }

          return data;
        } catch (err) {
          console.error("Error fulfilling sale promise:", err);
          throw err;
        }
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['promises'] });
      queryClient.invalidateQueries({ queryKey: ['sales'] });
    }
  });

  return {
    promises: promises || [],
    isLoading,
    error: promisesError,
    addPromise: addPromise.mutate,
    updatePromiseStatus: updatePromiseStatus.mutate,
    fulfillPromise: fulfillPromise.mutate
  };
}
