
import { DailyTransactionsCard } from "@/components/dashboard/DailyTransactionsCard";
import { TelemarketerStatsTable } from "@/components/dashboard/TelemarketerStatsTable";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useSupabasePromises } from "@/hooks/useSupabasePromises";
import { useSupabaseReminders } from "@/hooks/useSupabaseReminders";
import { SaleWithRelations, useSupabaseSales } from "@/hooks/useSupabaseSales";
import { useSupabaseTelemarketers } from "@/hooks/useSupabaseTelemarketers";
import { TimePeriod } from "@/types";
import { BarChart, Calendar, Filter, Phone, User, Users } from "lucide-react";
import { useEffect, useState } from "react";
import { Bar, CartesianGrid, Legend, BarChart as RechartsBarChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts";

interface TelemarketerPerformance {
  id?: string;
  name: string;
  sales: number;
  promises: number;
  reminders: number;
  advances: number;
  team_leader_id?: string | null;
  team_leader_name?: string | null;
}

export default function Dashboard() {
  const { sales, isLoading: isLoadingSales } = useSupabaseSales();
  const { promises, isLoading: isLoadingPromises } = useSupabasePromises();
  const { reminders, isLoading: isLoadingReminders } = useSupabaseReminders();
  const { telemarketers, isLoading: isLoadingTelemarketers } = useSupabaseTelemarketers();

  // Combined loading state
  const isLoading = isLoadingSales || isLoadingPromises || isLoadingReminders || isLoadingTelemarketers;
  const [selectedTimePeriod, setSelectedTimePeriod] = useState<TimePeriod>("day");
  const [telemarketerPerformance, setTelemarketerPerformance] = useState<TelemarketerPerformance[]>([]);

  // Filtres pour les ventes
  const [selectedStartDate, setSelectedStartDate] = useState<string>("");
  const [selectedEndDate, setSelectedEndDate] = useState<string>("");
  const [selectedTelemarketer, setSelectedTelemarketer] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");

  // État pour stocker les ventes filtrées
  const [filteredSales, setFilteredSales] = useState<SaleWithRelations[]>([]);

  // Effet pour filtrer les ventes en fonction des critères sélectionnés
  useEffect(() => {
    if (!Array.isArray(sales)) return;

    let filtered = [...sales];

    // Filtre par plage de dates
    if (selectedStartDate || selectedEndDate) {
      filtered = filtered.filter(sale => {
        const saleDate = new Date(sale.date);
        saleDate.setHours(0, 0, 0, 0);

        // Si une date de début est spécifiée
        if (selectedStartDate) {
          const startDate = new Date(selectedStartDate);
          startDate.setHours(0, 0, 0, 0);
          if (saleDate < startDate) return false;
        }

        // Si une date de fin est spécifiée
        if (selectedEndDate) {
          const endDate = new Date(selectedEndDate);
          endDate.setHours(23, 59, 59, 999); // Fin de la journée
          if (saleDate > endDate) return false;
        }

        return true;
      });
    }

    // Filtre par télévendeur
    if (selectedTelemarketer && selectedTelemarketer !== 'all') {
      filtered = filtered.filter(sale => sale.telemarketer_id === selectedTelemarketer);
    }

    // Filtre par statut
    if (selectedStatus && selectedStatus !== 'all') {
      filtered = filtered.filter(sale => sale.status === selectedStatus);
    }

    setFilteredSales(filtered);
  }, [sales, selectedStartDate, selectedEndDate, selectedTelemarketer, selectedStatus]);

  useEffect(() => {
    // Ensure all data is available before processing
    if (!telemarketers || telemarketers.length === 0) return;
    if (!Array.isArray(sales)) return;
    if (!Array.isArray(promises)) return;
    if (!Array.isArray(reminders)) return;

    // Create a map of telemarketer performances
    const performanceMap = new Map<string, TelemarketerPerformance>();

    // Initialize with all telemarketers
    telemarketers.forEach(t => {
      if (!t.archived) {
        performanceMap.set(t.id, {
          id: t.id,
          name: t.name,
          sales: 0,
          promises: 0,
          reminders: 0,
          advances: 0,
          team_leader_id: t.team_leader_id,
          team_leader_name: t.team_leader ? t.team_leader.name : null
        });
      }
    });

    // Filter data by time period
    const cutoffDate = getCutoffDate(selectedTimePeriod);

    // Count sales
    sales.forEach(sale => {
      if (new Date(sale.date) >= cutoffDate && performanceMap.has(sale.telemarketer_id)) {
        const telePerf = performanceMap.get(sale.telemarketer_id);
        if (telePerf) {
          // Compter les ventes payées
          if (sale.status === 'paid') {
            telePerf.sales += 1;
          }
          // Compter les avances
          else if (sale.status === 'advance') {
            telePerf.advances += 1;
          }
          performanceMap.set(sale.telemarketer_id, telePerf);
        }
      }
    });

    // Count promises
    promises.forEach(promise => {
      if (promise.due_date && new Date(promise.due_date) >= cutoffDate && performanceMap.has(promise.telemarketer_id)) {
        const telePerf = performanceMap.get(promise.telemarketer_id);
        if (telePerf) {
          telePerf.promises += 1;
          performanceMap.set(promise.telemarketer_id, telePerf);
        }
      }
    });

    // Count reminders
    reminders.forEach(reminder => {
      if (reminder.due_date && new Date(reminder.due_date) >= cutoffDate && performanceMap.has(reminder.telemarketer_id)) {
        const telePerf = performanceMap.get(reminder.telemarketer_id);
        if (telePerf) {
          telePerf.reminders += 1;
          performanceMap.set(reminder.telemarketer_id, telePerf);
        }
      }
    });

    // Convert map to array for chart rendering
    setTelemarketerPerformance(Array.from(performanceMap.values()));
  }, [telemarketers, sales, promises, reminders, selectedTimePeriod]);

  // Get today's sales
  const todaySales = Array.isArray(sales) ? sales.filter(sale =>
    new Date(sale.date).toDateString() === new Date().toDateString() &&
    (sale.status === 'paid' || sale.status === 'advance')
  ).length : 0;

  // Get today's revenue
  const todayRevenue = Array.isArray(sales) ? sales.filter(sale =>
    new Date(sale.date).toDateString() === new Date().toDateString() &&
    (sale.status === 'paid' || sale.status === 'advance')
  ).reduce((sum, sale) => sum + Number(sale.price), 0) : 0;

  // Get today's new customers (unique customer_ids from today's sales)
  const todayCustomers = Array.isArray(sales) ? new Set(
    sales.filter(sale =>
      new Date(sale.date).toDateString() === new Date().toDateString()
    ).map(sale => sale.customer_id)
  ).size : 0;

  // Afficher toutes les promesses pour le débogage
  console.log("All promises:", promises);

  // Inclure toutes les promesses, quelle que soit leur date d'échéance
  // Cela permettra d'afficher toutes les promesses dans le tableau de bord
  const todayPromises = Array.isArray(promises) ? promises : [];

  console.log("Promises to display:", todayPromises);

  // Afficher toutes les ventes pour le débogage
  console.log("All sales:", sales);

  // Afficher les statuts uniques des ventes
  const uniqueStatuses = Array.isArray(sales)
    ? [...new Set(sales.map(sale => sale.status))]
    : [];
  console.log("Unique sale statuses:", uniqueStatuses);

  // Inclure toutes les ventes avec statut 'advance', quelle que soit leur date
  // Cela permettra d'afficher toutes les avances dans le tableau de bord
  const todayAdvances = Array.isArray(sales)
    ? sales.filter(sale => sale.status === 'advance')
    : [];

  console.log("Advances to display:", todayAdvances);

  // Get upcoming reminders (due today or tomorrow)
  const upcomingReminders = Array.isArray(reminders) ? reminders.filter(reminder => {
    if (!reminder.due_date) return false;
    const dueDate = new Date(reminder.due_date);
    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);

    return (
      dueDate.toDateString() === today.toDateString() ||
      dueDate.toDateString() === tomorrow.toDateString()
    );
  }).length : 0;

  // Get top performers (by sales)
  const topPerformers = [...(telemarketerPerformance || [])]
    .sort((a, b) => b.sales - a.sales)
    .slice(0, 3);

  // Calculer les statistiques globales
  const globalStats = {
    totalSales: telemarketerPerformance.reduce((sum, t) => sum + t.sales, 0),
    totalPromises: telemarketerPerformance.reduce((sum, t) => sum + t.promises, 0),
    totalAdvances: telemarketerPerformance.reduce((sum, t) => sum + t.advances, 0)
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Tableau de Bord</h1>
      </div>

      {/* Section de filtrage des ventes */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="mr-2 h-4 w-4" />
            Filtrer les ventes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label htmlFor="start-date-filter">Date de début</Label>
                <Input
                  id="start-date-filter"
                  type="date"
                  value={selectedStartDate}
                  onChange={(e) => setSelectedStartDate(e.target.value)}
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="end-date-filter">Date de fin</Label>
                <Input
                  id="end-date-filter"
                  type="date"
                  value={selectedEndDate}
                  onChange={(e) => setSelectedEndDate(e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="telemarketer-filter">Télévendeur</Label>
              <Select value={selectedTelemarketer} onValueChange={setSelectedTelemarketer}>
                <SelectTrigger id="telemarketer-filter" className="mt-1">
                  <SelectValue placeholder="Tous les télévendeurs" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les télévendeurs</SelectItem>
                  {isLoadingTelemarketers ? (
                    <SelectItem value="loading" disabled>Chargement...</SelectItem>
                  ) : Array.isArray(telemarketers) && telemarketers.length > 0 ? (
                    telemarketers.filter(t => !t.archived).map(telemarketer => (
                      <SelectItem key={telemarketer.id} value={telemarketer.id}>
                        {telemarketer.name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="none" disabled>Aucun télévendeur trouvé</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="status-filter">Statut</Label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger id="status-filter" className="mt-1">
                  <SelectValue placeholder="Tous les statuts" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les statuts</SelectItem>
                  <SelectItem value="paid">Payé</SelectItem>
                  <SelectItem value="advance">Avance</SelectItem>
                  <SelectItem value="promise">Promesse</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedStartDate("");
                  setSelectedEndDate("");
                  setSelectedTelemarketer("all");
                  setSelectedStatus("all");
                }}
                className="w-full"
              >
                Réinitialiser les filtres
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue={selectedTimePeriod} onValueChange={(value) => setSelectedTimePeriod(value as TimePeriod)} className="space-y-4">
        <TabsList>
          <TabsTrigger value="day">Jour</TabsTrigger>
          <TabsTrigger value="week">Semaine</TabsTrigger>
          <TabsTrigger value="month">Mois</TabsTrigger>
        </TabsList>

        <TabsContent value="day" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Ventes Totales Aujourd'hui</CardTitle>
                <Phone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {isLoading ? <Skeleton className="h-8 w-16" /> : todaySales}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Revenus Aujourd'hui</CardTitle>
                <BarChart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {isLoading ? <Skeleton className="h-8 w-32" /> : `${todayRevenue.toLocaleString('fr-FR')} XAF`}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Nouveaux Clients</CardTitle>
                <User className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {isLoading ? <Skeleton className="h-8 w-16" /> : todayCustomers}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Rappels à Venir</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {isLoading ? <Skeleton className="h-8 w-16" /> : upcomingReminders}
                </div>
              </CardContent>
            </Card>

          </div>

          <Card className="col-span-4">
            <CardHeader>
              <CardTitle>Meilleurs Performeurs</CardTitle>
              <CardDescription>
                Télévendeurs avec le plus de ventes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {isLoading ? (
                  <>
                    <div className="flex items-center space-x-4">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-4 w-24" />
                      </div>
                      <Skeleton className="h-4 w-20" />
                    </div>
                    <div className="flex items-center space-x-4">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-4 w-24" />
                      </div>
                      <Skeleton className="h-4 w-20" />
                    </div>
                    <div className="flex items-center space-x-4">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-4 w-24" />
                      </div>
                      <Skeleton className="h-4 w-20" />
                    </div>
                  </>
                ) : (
                  <>
                    {topPerformers.map((telemarketer, i) => (
                      <div key={i} className="flex items-center">
                        <div className="mr-4 flex h-10 w-10 items-center justify-center rounded-full bg-primary text-primary-foreground">
                          <Users className="h-5 w-5" />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium leading-none">{telemarketer.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {telemarketer.sales} ventes, {telemarketer.promises} promesses
                          </p>
                        </div>
                        <div className="font-bold">
                          {sales
                            ?.filter(sale => sale.telemarketer_id === telemarketers.find(t => t.name === telemarketer.name)?.id)
                            .reduce((sum, sale) => sum + Number(sale.price), 0)
                            .toLocaleString('fr-FR')} XAF
                        </div>
                      </div>
                    ))}
                    {topPerformers.length === 0 && !isLoading && (
                      <div className="text-center py-4 text-muted-foreground italic">
                        Aucune donnée disponible pour cette période
                      </div>
                    )}
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="week" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Ventes Totales Cette Semaine</CardTitle>
                <Phone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {sales?.filter(sale => {
                    const saleDate = new Date(sale.date);
                    return isInCurrentWeek(saleDate);
                  }).length || 0}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Revenus Cette Semaine</CardTitle>
                <BarChart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {sales?.filter(sale => {
                    const saleDate = new Date(sale.date);
                    return isInCurrentWeek(saleDate);
                  }).reduce((sum, sale) => sum + Number(sale.price), 0).toLocaleString('fr-FR')} XAF
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="month" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Ventes Totales Ce Mois</CardTitle>
                <Phone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {sales?.filter(sale => {
                    const saleDate = new Date(sale.date);
                    const now = new Date();
                    return saleDate.getMonth() === now.getMonth() &&
                      saleDate.getFullYear() === now.getFullYear();
                  }).length || 0}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Revenus Ce Mois</CardTitle>
                <BarChart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {sales?.filter(sale => {
                    const saleDate = new Date(sale.date);
                    const now = new Date();
                    return saleDate.getMonth() === now.getMonth() &&
                      saleDate.getFullYear() === now.getFullYear();
                  }).reduce((sum, sale) => sum + Number(sale.price), 0).toLocaleString('fr-FR')} XAF
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Statistiques globales */}
      <Card>
        <CardHeader>
          <CardTitle>Statistiques Globales</CardTitle>
          <CardDescription>Résumé des performances pour la période sélectionnée</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center p-4 bg-muted rounded-lg">
              <h3 className="text-lg font-semibold">Ventes</h3>
              <p className="text-3xl font-bold">
                {isLoading ? <Skeleton className="h-8 w-16 mx-auto" /> : globalStats.totalSales}
              </p>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <h3 className="text-lg font-semibold">Promesses</h3>
              <p className="text-3xl font-bold">
                {isLoading ? <Skeleton className="h-8 w-16 mx-auto" /> : globalStats.totalPromises}
              </p>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <h3 className="text-lg font-semibold">Avances</h3>
              <p className="text-3xl font-bold">
                {isLoading ? <Skeleton className="h-8 w-16 mx-auto" /> : globalStats.totalAdvances}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Transactions du jour (promesses et avances) */}
      <DailyTransactionsCard
        promises={todayPromises}
        advances={todayAdvances}
        isLoading={isLoading}
      />

      {/* Résultats de filtrage */}
      {(selectedStartDate || selectedEndDate || selectedTelemarketer !== "all" || selectedStatus !== "all") && (
        <Card>
          <CardHeader>
            <CardTitle>Résultats du Filtrage</CardTitle>
            <CardDescription>
              {filteredSales.length} vente(s) trouvée(s)
              {selectedStartDate && selectedEndDate && (
                <span className="block text-sm mt-1">
                  Période: du {new Date(selectedStartDate).toLocaleDateString('fr-FR')} au {new Date(selectedEndDate).toLocaleDateString('fr-FR')}
                </span>
              )}
              {selectedStartDate && !selectedEndDate && (
                <span className="block text-sm mt-1">
                  Période: à partir du {new Date(selectedStartDate).toLocaleDateString('fr-FR')}
                </span>
              )}
              {!selectedStartDate && selectedEndDate && (
                <span className="block text-sm mt-1">
                  Période: jusqu'au {new Date(selectedEndDate).toLocaleDateString('fr-FR')}
                </span>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredSales.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Client</TableHead>
                    <TableHead>Télévendeur</TableHead>
                    <TableHead>Formation</TableHead>
                    <TableHead>Prix</TableHead>
                    <TableHead>Statut</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSales.map((sale) => (
                    <TableRow key={sale.id}>
                      <TableCell>{new Date(sale.date).toLocaleDateString('fr-FR')}</TableCell>
                      <TableCell>{sale.customers?.name || 'N/A'}</TableCell>
                      <TableCell>{sale.telemarketers?.name || 'N/A'}</TableCell>
                      <TableCell>{sale.courses?.name || 'N/A'}</TableCell>
                      <TableCell>{Number(sale.price).toLocaleString('fr-FR')} XAF</TableCell>
                      <TableCell>
                        <Badge className={`bg-status-${sale.status}`}>
                          {sale.status === 'paid' && 'Payé'}
                          {sale.status === 'advance' && 'Avance'}
                          {sale.status === 'promise' && 'Promesse'}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                Aucune vente ne correspond aux critères de filtrage
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Graphique de performance */}
      <Card>
        <CardHeader>
          <CardTitle>Performance des Télévendeurs</CardTitle>
          <CardDescription>Corrélation entre ventes, promesses et rappels</CardDescription>
        </CardHeader>
        <CardContent className="h-80">
          {isLoading ? (
            <div className="h-full w-full flex flex-col justify-center space-y-4">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <RechartsBarChart
                data={telemarketerPerformance}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="sales" name="Ventes" fill="#8884d8" />
                <Bar dataKey="promises" name="Promesses" fill="#82ca9d" />
                <Bar dataKey="advances" name="Avances" fill="#fb7185" />
                <Bar dataKey="reminders" name="Rappels" fill="#ffc658" />
              </RechartsBarChart>
            </ResponsiveContainer>
          )}
        </CardContent>
      </Card>

      {/* Tableau des statistiques par équipe */}
      <TelemarketerStatsTable
        data={telemarketerPerformance}
        title="Statistiques par Équipe"
        description={`Performances des équipes pour la période: ${selectedTimePeriod === 'day' ? 'Jour' : selectedTimePeriod === 'week' ? 'Semaine' : 'Mois'}`}
        isLoading={isLoading}
        groupByTeam={true}
      />

      {/* Tableau détaillé des performances par télévendeur */}
      <TelemarketerStatsTable
        data={telemarketerPerformance}
        title="Statistiques Détaillées par Télévendeur"
        description={`Performances individuelles pour la période: ${selectedTimePeriod === 'day' ? 'Jour' : selectedTimePeriod === 'week' ? 'Semaine' : 'Mois'}`}
        isLoading={isLoading}
      />
    </div>
  );
}

// Helper function to determine if a date is in the current week
function isInCurrentWeek(date: Date): boolean {
  const now = new Date();
  const startOfWeek = new Date(now);
  startOfWeek.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
  startOfWeek.setHours(0, 0, 0, 0);

  const endOfWeek = new Date(now);
  endOfWeek.setDate(now.getDate() + (6 - now.getDay())); // End of week (Saturday)
  endOfWeek.setHours(23, 59, 59, 999);

  return date >= startOfWeek && date <= endOfWeek;
}

// Helper function to get cutoff date based on time period
function getCutoffDate(period: TimePeriod): Date {
  const now = new Date();
  const cutoffDate = new Date(now);

  switch (period) {
    case "day":
      cutoffDate.setHours(0, 0, 0, 0);
      break;
    case "week":
      cutoffDate.setDate(now.getDate() - now.getDay());
      cutoffDate.setHours(0, 0, 0, 0);
      break;
    case "month":
      cutoffDate.setDate(1);
      cutoffDate.setHours(0, 0, 0, 0);
      break;
    default:
      cutoffDate.setDate(now.getDate() - 30);
  }

  return cutoffDate;
}
