
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSupabaseCourses } from "@/hooks/useSupabaseCourses";
import { useSupabaseCustomers } from "@/hooks/useSupabaseCustomers";
import { useSupabaseTelemarketers } from "@/hooks/useSupabaseTelemarketers";
import { TelemarketerStats } from "@/types";
import { PlusCircle } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { ComboboxSelect } from "./ComboboxSelect";
import { MultiComboboxSelect } from "./MultiComboboxSelect";

interface Course {
  id: string;
  name: string;
  price: number;
}

interface Customer {
  id: string;
  name: string;
  email?: string | null;
  phone?: string | null;
}

interface AddSaleDialogProps {
  onSaleAdd: (sale: any) => void;
  telemarketers: TelemarketerStats[];
  courses: Course[];
  customers: Customer[];
}

export function AddSaleDialog({ onSaleAdd, telemarketers = [], courses = [], customers = [] }: AddSaleDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [newSale, setNewSale] = useState({
    customer_id: "",
    telemarketer_id: "",
    course_ids: [] as string[],
    price: "",
    original_price: "",
    discount: 0,
    status: "paid" as "paid" | "advance" | "promise" | "reminder",
    date: new Date().toISOString().split('T')[0], // Date de la vente (aujourd'hui par défaut)
    due_date: "",
    advance_amount: "",
    advance_due_date: ""
  });

  const [showCustomerForm, setShowCustomerForm] = useState(false);
  const [showTelemarketerForm, setShowTelemarketerForm] = useState(false);
  const [showCourseForm, setShowCourseForm] = useState(false);

  const [newCustomer, setNewCustomer] = useState({
    name: "",
    email: "",
    phone: "",
  });

  const [newTelemarketer, setNewTelemarketer] = useState({
    name: "",
    email: "",
    phone: "",
    team_leader_id: ""
  });

  const [newCourse, setNewCourse] = useState({
    name: "",
    price: ""
  });

  const [validationErrors, setValidationErrors] = useState({
    customer: false,
    telemarketer: false,
    course: false
  });

  const { addCustomer } = useSupabaseCustomers();
  const { addTelemarketer } = useSupabaseTelemarketers();
  const { addCourse } = useSupabaseCourses();

  // Ensure proper data type validation for options
  const safeCustomers = Array.isArray(customers) ? customers : [];
  const safeTelemarketers = Array.isArray(telemarketers) ? telemarketers : [];
  const safeCourses = Array.isArray(courses) ? courses : [];

  const customerOptions = safeCustomers
    .filter(c => c && typeof c === 'object' && c.id && typeof c.name === 'string')
    .map(c => ({ label: c.name, value: c.id }));

  const telemarketerOptions = safeTelemarketers
    .filter(t => t && typeof t === 'object' && t.id && typeof t.name === 'string' && !t.archived)
    .map(t => ({ label: t.name, value: t.id }));

  const courseOptions = safeCourses
    .filter(c => c && typeof c === 'object' && c.id && typeof c.name === 'string')
    .map(c => ({ label: c.name, value: c.id }));

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString("fr-FR", { style: "currency", currency: "XOF" });
  };

  const handleCoursesSelect = (courseIds: string[]) => {
    if (!courseIds.length) {
      setNewSale({
        ...newSale,
        course_ids: [],
        price: "",
        original_price: "",
        discount: 0
      });
      return;
    }

    // Calculer le prix total des formations sélectionnées
    let totalPrice = 0;
    courseIds.forEach(courseId => {
      const course = safeCourses.find(c => c && c.id === courseId);
      if (course) {
        totalPrice += course.price;
      }
    });

    setNewSale({
      ...newSale,
      course_ids: courseIds,
      price: totalPrice.toString(),
      original_price: totalPrice.toString(),
      discount: 0
    });

    setValidationErrors(prev => ({ ...prev, course: false }));
  };

  const handleDiscountChange = (discountPercent: number) => {
    if (discountPercent < 0 || discountPercent > 100) return;

    const originalPrice = parseFloat(newSale.original_price);
    if (isNaN(originalPrice)) return;

    const discountAmount = originalPrice * (discountPercent / 100);
    const finalPrice = originalPrice - discountAmount;

    setNewSale({
      ...newSale,
      discount: discountPercent,
      price: finalPrice.toString()
    });
  };

  const handleAddSale = () => {
    const errors = {
      customer: !newSale.customer_id,
      telemarketer: !newSale.telemarketer_id,
      course: !newSale.course_ids.length
    };

    setValidationErrors(errors);

    if (errors.customer || errors.telemarketer || errors.course) {
      if (errors.customer) toast.error("Veuillez sélectionner un client");
      if (errors.telemarketer) toast.error("Veuillez sélectionner un télévendeur");
      if (errors.course) toast.error("Veuillez sélectionner au moins une formation");
      return;
    }

    if (isNaN(parseFloat(newSale.price)) || parseFloat(newSale.price) <= 0) {
      toast.error("Veuillez saisir un prix valide");
      return;
    }

    // Vérifier si une date d'échéance est requise mais non fournie
    if ((newSale.status === 'promise' || newSale.status === 'reminder') && !newSale.due_date) {
      toast.error("Veuillez saisir une date d'échéance");
      return;
    }

    // Vérifier si une date limite de paiement est requise pour les avances
    if (newSale.status === 'advance' && !newSale.advance_due_date) {
      toast.error("Veuillez saisir une date limite de paiement");
      return;
    }

    // Vérifier si un montant d'avance est requis pour les avances
    if (newSale.status === 'advance' && (!newSale.advance_amount || parseFloat(newSale.advance_amount) <= 0)) {
      toast.error("Veuillez saisir un montant d'avance valide");
      return;
    }

    const today = new Date().toISOString().split('T')[0]; // Format as YYYY-MM-DD

    // Récupérer les détails des formations sélectionnées
    const selectedCourses = newSale.course_ids.map(courseId => {
      const course = safeCourses.find(c => c.id === courseId);
      return {
        id: courseId,
        name: course?.name || "",
        price: course?.price || 0
      };
    });

    const sale = {
      ...newSale,
      price: parseFloat(newSale.price),
      original_price: parseFloat(newSale.original_price),
      discount: newSale.discount,
      date: newSale.date || today, // Utiliser la date sélectionnée par l'utilisateur ou la date du jour
      // La date d'échéance est obligatoire dans la base de données
      due_date: newSale.status === 'advance'
        ? newSale.advance_due_date // Pour les avances, utiliser la date limite de paiement
        : (newSale.status === 'promise' || newSale.status === 'reminder')
          ? newSale.due_date // Pour les promesses, utiliser la date d'échéance
          : today, // Utiliser la date du jour pour les ventes payées
      advance_amount: newSale.status === 'advance' && newSale.advance_amount
        ? parseFloat(newSale.advance_amount)
        : null,
      advance_due_date: newSale.status === 'advance' && newSale.advance_due_date
        ? newSale.advance_due_date // Already in YYYY-MM-DD format from input type="date"
        : null,
      courses: selectedCourses
    };

    if (typeof onSaleAdd === 'function') {
      onSaleAdd(sale);
    }
    setIsOpen(false);
    resetForm();
  };

  const handleAddCustomer = () => {
    if (!newCustomer.name.trim()) {
      toast.error("Le nom du client est requis");
      return;
    }

    addCustomer(
      {
        name: newCustomer.name.trim(),
        email: newCustomer.email.trim() || null,
        phone: newCustomer.phone.trim() || null
      },
      {
        onSuccess: (customer) => {
          toast.success("Client ajouté avec succès");
          setNewSale({ ...newSale, customer_id: customer.id });
          setValidationErrors(prev => ({ ...prev, customer: false }));
          setShowCustomerForm(false);
        },
        onError: (error) => {
          toast.error("Erreur lors de l'ajout du client");
          console.error("Error adding customer:", error);
        }
      }
    );
  };

  const handleAddTelemarketer = () => {
    if (!newTelemarketer.name.trim()) {
      toast.error("Le nom du télévendeur est requis");
      return;
    }

    addTelemarketer(
      {
        name: newTelemarketer.name.trim(),
        email: newTelemarketer.email.trim() || null,
        phone: newTelemarketer.phone.trim() || null,
        team_leader_id: newTelemarketer.team_leader_id || null
      },
      {
        onSuccess: (telemarketer) => {
          toast.success("Télévendeur ajouté avec succès");
          setNewSale({ ...newSale, telemarketer_id: telemarketer.id });
          setValidationErrors(prev => ({ ...prev, telemarketer: false }));
          setShowTelemarketerForm(false);
        },
        onError: (error) => {
          toast.error("Erreur lors de l'ajout du télévendeur");
          console.error("Error adding telemarketer:", error);
        }
      }
    );
  };

  const handleAddCourse = () => {
    if (!newCourse.name.trim()) {
      toast.error("Le nom de la formation est requis");
      return;
    }

    const price = parseFloat(newCourse.price);
    if (isNaN(price) || price <= 0) {
      toast.error("Veuillez saisir un prix valide");
      return;
    }

    addCourse(
      {
        name: newCourse.name.trim(),
        price: price
      },
      {
        onSuccess: (course) => {
          toast.success("Formation ajoutée avec succès");
          // Ajouter la nouvelle formation à la liste des formations sélectionnées
          const newCourseIds = [...newSale.course_ids, course.id];
          handleCoursesSelect(newCourseIds);
          setValidationErrors(prev => ({ ...prev, course: false }));
          setShowCourseForm(false);
        },
        onError: (error) => {
          toast.error("Erreur lors de l'ajout de la formation");
          console.error("Error adding course:", error);
        }
      }
    );
  };

  const resetForm = () => {
    setNewSale({
      customer_id: "",
      telemarketer_id: "",
      course_ids: [],
      price: "",
      original_price: "",
      discount: 0,
      status: "paid" as "paid" | "advance" | "promise" | "reminder",
      date: new Date().toISOString().split('T')[0], // Réinitialiser à la date du jour
      due_date: "",
      advance_amount: "",
      advance_due_date: ""
    });
    setNewCustomer({ name: "", email: "", phone: "" });
    setNewTelemarketer({ name: "", email: "", phone: "", team_leader_id: "" });
    setNewCourse({ name: "", price: "" });
    setShowCustomerForm(false);
    setShowTelemarketerForm(false);
    setShowCourseForm(false);
    setValidationErrors({ customer: false, telemarketer: false, course: false });
  };

  useEffect(() => {
    if (isOpen) {
      resetForm();
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      setIsOpen(open);
      if (!open) {
        resetForm();
      }
    }}>
      <DialogTrigger asChild>
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          Ajouter une vente
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-[95vh] max-w-[600px] pb-4 overflow-hidden" onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogHeader className="pb-2">
          <DialogTitle>Enregistrer une nouvelle vente</DialogTitle>
          <DialogDescription>
            Remplissez les informations pour ajouter une nouvelle vente ou promesse
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col h-full max-h-[calc(95vh-10rem)] min-h-0">
          <ScrollArea className="flex-1 pr-4 overflow-y-auto">
            <div className="grid gap-3 py-2 px-1">
              <div className="grid gap-1">
                <Label>Client {validationErrors.customer && <span className="text-destructive">*</span>}</Label>
                {!showCustomerForm ? (
                  <div className="space-y-1">
                    <ComboboxSelect
                      options={customerOptions}
                      value={newSale.customer_id}
                      onValueChange={(value) => {
                        setNewSale({ ...newSale, customer_id: value });
                        setValidationErrors(prev => ({ ...prev, customer: false }));
                      }}
                      placeholder="Sélectionnez un client"
                      searchPlaceholder="Rechercher un client..."
                    />
                    <Button
                      variant="link"
                      className="px-0"
                      onClick={() => setShowCustomerForm(true)}
                      type="button"
                    >
                      Client non trouvé ? Ajouter un nouveau client
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-1">
                    <Input
                      placeholder="Nom du client"
                      value={newCustomer.name}
                      onChange={(e) => {
                        setNewCustomer({ ...newCustomer, name: e.target.value });
                      }}
                    />
                    <Input
                      placeholder="Email"
                      type="email"
                      value={newCustomer.email}
                      onChange={(e) => setNewCustomer({ ...newCustomer, email: e.target.value })}
                    />
                    <Input
                      placeholder="Téléphone"
                      type="tel"
                      value={newCustomer.phone}
                      onChange={(e) => setNewCustomer({ ...newCustomer, phone: e.target.value })}
                    />
                    <div className="flex justify-between">
                      <Button
                        variant="link"
                        className="px-0"
                        onClick={() => setShowCustomerForm(false)}
                      >
                        Retour à la sélection du client
                      </Button>
                      <Button
                        variant="secondary"
                        onClick={handleAddCustomer}
                      >
                        Ajouter le client
                      </Button>
                    </div>
                  </div>
                )}
              </div>

              <div className="grid gap-1">
                <Label>Télévendeur {validationErrors.telemarketer && <span className="text-destructive">*</span>}</Label>
                {!showTelemarketerForm ? (
                  <div className="space-y-1">
                    <ComboboxSelect
                      options={telemarketerOptions}
                      value={newSale.telemarketer_id}
                      onValueChange={(value) => {
                        setNewSale({ ...newSale, telemarketer_id: value });
                        setValidationErrors(prev => ({ ...prev, telemarketer: false }));
                      }}
                      placeholder="Sélectionnez le télévendeur"
                      searchPlaceholder="Rechercher un télévendeur..."
                    />
                    <Button
                      variant="link"
                      className="px-0"
                      onClick={() => setShowTelemarketerForm(true)}
                      type="button"
                    >
                      Télévendeur non trouvé ? Ajouter un nouveau télévendeur
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-1">
                    <Input
                      placeholder="Nom du télévendeur"
                      value={newTelemarketer.name}
                      onChange={(e) => {
                        setNewTelemarketer({ ...newTelemarketer, name: e.target.value });
                      }}
                    />
                    <Input
                      placeholder="Email"
                      type="email"
                      value={newTelemarketer.email}
                      onChange={(e) => setNewTelemarketer({ ...newTelemarketer, email: e.target.value })}
                    />
                    <Input
                      placeholder="Téléphone"
                      type="tel"
                      value={newTelemarketer.phone}
                      onChange={(e) => setNewTelemarketer({ ...newTelemarketer, phone: e.target.value })}
                    />
                    <Label>Chef d'équipe (optionnel)</Label>
                    <ComboboxSelect
                      options={telemarketerOptions}
                      value={newTelemarketer.team_leader_id}
                      onValueChange={(value) => {
                        setNewTelemarketer({ ...newTelemarketer, team_leader_id: value });
                      }}
                      placeholder="Sélectionnez un chef d'équipe"
                      searchPlaceholder="Rechercher un chef d'équipe..."
                    />
                    <div className="flex justify-between">
                      <Button
                        variant="link"
                        className="px-0"
                        onClick={() => setShowTelemarketerForm(false)}
                      >
                        Retour à la sélection du télévendeur
                      </Button>
                      <Button
                        variant="secondary"
                        onClick={handleAddTelemarketer}
                      >
                        Ajouter le télévendeur
                      </Button>
                    </div>
                  </div>
                )}
              </div>

              <div className="grid gap-1">
                <Label>Formation {validationErrors.course && <span className="text-destructive">*</span>}</Label>
                {!showCourseForm ? (
                  <div className="space-y-1">
                    <MultiComboboxSelect
                      options={courseOptions}
                      values={newSale.course_ids}
                      onValuesChange={handleCoursesSelect}
                      placeholder="Sélectionnez une ou plusieurs formations"
                      searchPlaceholder="Rechercher une formation..."
                    />
                    <Button
                      variant="link"
                      className="px-0"
                      onClick={() => setShowCourseForm(true)}
                      type="button"
                    >
                      Formation non trouvée ? Ajouter une nouvelle formation
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-1">
                    <Input
                      placeholder="Nom de la formation"
                      value={newCourse.name}
                      onChange={(e) => {
                        setNewCourse({ ...newCourse, name: e.target.value });
                      }}
                    />
                    <Input
                      placeholder="Prix"
                      type="number"
                      value={newCourse.price}
                      onChange={(e) => setNewCourse({ ...newCourse, price: e.target.value })}
                    />
                    <div className="flex justify-between">
                      <Button
                        variant="link"
                        className="px-0"
                        onClick={() => setShowCourseForm(false)}
                      >
                        Retour à la sélection de la formation
                      </Button>
                      <Button
                        variant="secondary"
                        onClick={handleAddCourse}
                      >
                        Ajouter la formation
                      </Button>
                    </div>
                  </div>
                )}
              </div>

              <div className="grid gap-1">
                <Label>Prix original</Label>
                <Input
                  type="number"
                  value={newSale.original_price}
                  readOnly
                  className="bg-muted"
                  placeholder="0 XAF"
                />
                {newSale.original_price && (
                  <div className="text-sm text-muted-foreground">
                    {formatCurrency(parseFloat(newSale.original_price))}
                  </div>
                )}
              </div>

              <div className="grid gap-1">
                <Label>Réduction (%)</Label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  value={newSale.discount}
                  onChange={(e) => handleDiscountChange(parseFloat(e.target.value) || 0)}
                  placeholder="0%"
                />
              </div>

              <div className="grid gap-1">
                <Label>Prix final</Label>
                <Input
                  type="number"
                  value={newSale.price}
                  readOnly
                  className="bg-muted"
                  placeholder="0 XAF"
                />
                {newSale.price && (
                  <div className="text-sm text-muted-foreground">
                    {formatCurrency(parseFloat(newSale.price))}
                    {newSale.discount > 0 && (
                      <span className="ml-2 text-green-600">
                        (-{newSale.discount}%)
                      </span>
                    )}
                  </div>
                )}
              </div>
              <div className="grid gap-1">
                <Label>Type d'enregistrement</Label>
                <Select
                  value={newSale.status}
                  onValueChange={(value: "paid" | "advance" | "promise" | "reminder") => setNewSale({ ...newSale, status: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionnez le type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="paid">Payé</SelectItem>
                    <SelectItem value="advance">Avance</SelectItem>
                    <SelectItem value="promise">Promesse de paiement</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-1">
                <Label>Date de la vente</Label>
                <Input
                  type="date"
                  value={newSale.date}
                  onChange={(e) => setNewSale({ ...newSale, date: e.target.value })}
                />
              </div>

              {(newSale.status === "promise") && (
                <div className="grid gap-1">
                  <Label>Date d'échéance</Label>
                  <Input
                    type="date"
                    value={newSale.due_date}
                    onChange={(e) => setNewSale({ ...newSale, due_date: e.target.value })}
                  />
                </div>
              )}

              {(newSale.status === "advance") && (
                <>
                  <div className="grid gap-1">
                    <Label>Montant de l'avance</Label>
                    <Input
                      type="number"
                      min="0"
                      max={newSale.price}
                      value={newSale.advance_amount}
                      onChange={(e) => setNewSale({ ...newSale, advance_amount: e.target.value })}
                      placeholder="Montant de l'avance"
                    />
                    {newSale.advance_amount && newSale.price && (
                      <div className="text-sm text-muted-foreground">
                        {formatCurrency(parseFloat(newSale.advance_amount))} sur {formatCurrency(parseFloat(newSale.price))}
                        {" "}
                        ({Math.round((parseFloat(newSale.advance_amount) / parseFloat(newSale.price)) * 100)}%)
                      </div>
                    )}
                  </div>
                  <div className="grid gap-1">
                    <Label>Date limite de paiement</Label>
                    <Input
                      type="date"
                      value={newSale.advance_due_date}
                      onChange={(e) => setNewSale({ ...newSale, advance_due_date: e.target.value })}
                    />
                  </div>
                </>
              )}
            </div>
          </ScrollArea>
        </div>
        <DialogFooter className="pt-2 mt-1">
          <Button onClick={handleAddSale}>
            {newSale.status === "promise" ? "Enregistrer la promesse" : "Enregistrer la vente"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
