# Call Center Nexus Application

## Project Overview

Call Center Nexus is a comprehensive call center management application designed for training and managing telemarketers, tracking sales, promises, and reminders. The application features a French-language interface and integrates with Supabase for backend data management.

## Project Structure & Tech Stack

- **Frontend Framework**: React 18.3.1 with TypeScript 5.5.3
- **Build Tool**: Vite 5.4.1
- **UI Components**: Shadcn UI with Radix UI and Tailwind CSS
- **State Management**: TanStack React Query 5.56.2
- **Routing**: React Router Dom 6.26.2
- **Data Visualization**: Recharts 2.12.7
- **Database**: Supabase (PostgreSQL)
- **Form Handling**: React Hook Form 7.53.0 with Zod validation
- **Icons**: Lucide React
- **Notifications**: Sonner & Toast components

## Features

1. **Dashboard**
   - Display of key metrics: daily sales, revenue, new customers, upcoming reminders
   - Performance charts for telemarketers
   - Time period filters (day, week, month)
   - Top performer rankings

2. **Customer Management**
   - Add, view and manage customer contacts
   - Customer details including name, email, and phone
   - Customer history tracking

3. **Telemarketer Management**
   - Add, edit, and archive telemarketers
   - Performance tracking and metrics
   - Activity monitoring

4. **Course (Product) Management**
   - Manage available courses/products
   - Set and update pricing

5. **Sales Tracking**
   - Record new sales
   - Track sale status (paid, advance, promise, reminder)
   - Link sales to customers, telemarketers, and courses
   - Due date management for payments

6. **Promise Management**
   - Track customer promises to pay
   - Multiple status options (pending, postponed, fulfilled, reminder-sent)
   - Due date tracking and updates
   - Reminder counting

7. **Reminder System**
   - Schedule follow-up reminders
   - Track reminder status
   - Due date management
   - Assign to telemarketers

8. **Advance Payment Tracking**
   - Record partial payments against sales
   - Track payment dates and amounts

## Data Model

### Tables

1. **customers**
   - id, name, email, phone, created_at

2. **telemarketers**
   - id, name, email, phone, archived, created_at

3. **courses**
   - id, name, price, created_at

4. **sales**
   - id, customer_id, telemarketer_id, course_id, price, date, due_date, status, created_at

5. **promises**
   - id, customer_id, telemarketer_id, course_id, amount, due_date, original_due_date, status, reminders, created_at

6. **reminders**
   - id, customer_id, telemarketer_id, course_id, amount, due_date, status, created_at

7. **advance_payments**
   - id, sale_id, amount, payment_date, created_at

### Enums

1. **promise_status**: "pending" | "postponed" | "fulfilled" | "reminder-sent"
2. **sale_status**: "paid" | "advance" | "promise" | "reminder"

## UI Features

- **Responsive Layout**: Mobile and desktop friendly
- **Language**: French interface
- **Navigation**: Sidebar menu for easy access to all features
- **Data Visualization**: Charts and statistics for performance tracking
- **Notifications**: Toast notifications for user feedback
- **Forms**: Validated input forms with error handling
- **Tables**: Sortable and filterable data tables
- **Modals**: Clean dialog interfaces for data entry

## Implementation Instructions

1. **Project Setup**
   ```bash
   npm install
   npm run dev
   ```

2. **Environment Configuration**
   - Create a Supabase project
   - Set up the database schema according to the data model
   - Update the Supabase client credentials in `src/integrations/supabase/client.ts`

3. **Database Schema**
   - Create all tables with proper relationships
   - Set up enums for status fields
   - Create indexes for frequently queried fields

4. **Key Development Areas**
   - React components with Typescript interfaces
   - Supabase hooks for data fetching and mutations
   - Form validation with Zod schemas
   - Responsive UI with Tailwind CSS

## Deployment

- **Build Process**: `npm run build`
- **Hosting**: Deploy the `dist` directory to any static hosting service
- **Environment Variables**: Set up production Supabase credentials

## Security Considerations

- All database access is through Supabase Row Level Security policies
- Anon key is used for public access with appropriate permissions
- No sensitive information is stored in the frontend code

## Constraints & Technical Requirements

- Node.js >= 18.x
- Modern browser support (no IE11)
- Supabase for database backend
- Static hosting capability

## UX Guidelines

- Simple, intuitive interface for call center operators
- Quick data entry forms for fast customer interactions
- Clear visualizations for performance monitoring
- Easy navigation between related records (customer → sales → promises)

## Customization Options

- Theming through Tailwind configuration
- Adding new status types through database enums
- Extending data models with additional fields

## Project Expansion Ideas

- Authentication system for different user roles
- Multi-language support
- Advanced reporting and analytics
- Integration with telephony systems
- Email/SMS notification system for reminders
