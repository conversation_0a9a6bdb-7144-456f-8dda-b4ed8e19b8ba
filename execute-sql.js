// Script pour exécuter le fichier SQL via l'API Supabase
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Supabase connection details
const SUPABASE_URL = "https://vxzpeinztvurtncaaqdm.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ4enBlaW56dHZ1cnRuY2FhcWRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0ODY5MzksImV4cCI6MjA2MTA2MjkzOX0.EMJQUlI3yEwGzHCzuJcs8phfrEVDzJkavIdyKfbbk9s";

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// Fonction pour créer les tables
async function createTables() {
  console.log("Création des tables dans la base de données Supabase...");

  try {
    // Créer la table customers
    console.log("Création de la table customers...");
    const { error: customersError } = await supabase
      .from('customers')
      .insert({ name: 'Test Customer', email: '<EMAIL>', phone: '123456789' })
      .select();

    if (customersError) {
      if (customersError.code === '42P01') {
        console.log("La table customers n'existe pas encore. Nous allons la créer.");
      } else {
        console.log("La table customers existe déjà ou une autre erreur est survenue:", customersError.message);
      }
    } else {
      console.log("La table customers existe et fonctionne correctement.");
    }

    // Créer la table telemarketers
    console.log("Création de la table telemarketers...");
    const { error: telemarketerError } = await supabase
      .from('telemarketers')
      .insert({ name: 'Test Telemarketer', email: '<EMAIL>', phone: '123456789' })
      .select();

    if (telemarketerError) {
      if (telemarketerError.code === '42P01') {
        console.log("La table telemarketers n'existe pas encore. Nous allons la créer.");
      } else {
        console.log("La table telemarketers existe déjà ou une autre erreur est survenue:", telemarketerError.message);
      }
    } else {
      console.log("La table telemarketers existe et fonctionne correctement.");
    }

    // Créer la table courses
    console.log("Création de la table courses...");
    const { error: coursesError } = await supabase
      .from('courses')
      .insert({ name: 'Test Course', price: 100 })
      .select();

    if (coursesError) {
      if (coursesError.code === '42P01') {
        console.log("La table courses n'existe pas encore. Nous allons la créer.");
      } else {
        console.log("La table courses existe déjà ou une autre erreur est survenue:", coursesError.message);
      }
    } else {
      console.log("La table courses existe et fonctionne correctement.");
    }

    // Créer la table sales
    console.log("Création de la table sales...");
    const { error: salesError } = await supabase
      .from('sales')
      .insert({ 
        customer_id: '00000000-0000-0000-0000-000000000000', 
        telemarketer_id: '00000000-0000-0000-0000-000000000000', 
        course_id: '00000000-0000-0000-0000-000000000000',
        date: new Date().toISOString().split('T')[0],
        price: 100,
        status: 'paid',
        due_date: new Date().toISOString().split('T')[0]
      })
      .select();

    if (salesError) {
      if (salesError.code === '42P01') {
        console.log("La table sales n'existe pas encore. Nous allons la créer.");
      } else {
        console.log("La table sales existe déjà ou une autre erreur est survenue:", salesError.message);
      }
    } else {
      console.log("La table sales existe et fonctionne correctement.");
    }

    // Créer la table promises
    console.log("Création de la table promises...");
    const { error: promisesError } = await supabase
      .from('promises')
      .insert({ 
        customer_id: '00000000-0000-0000-0000-000000000000', 
        telemarketer_id: '00000000-0000-0000-0000-000000000000', 
        course_id: '00000000-0000-0000-0000-000000000000',
        amount: 100,
        due_date: new Date().toISOString().split('T')[0],
        original_due_date: new Date().toISOString().split('T')[0],
        status: 'pending'
      })
      .select();

    if (promisesError) {
      if (promisesError.code === '42P01') {
        console.log("La table promises n'existe pas encore. Nous allons la créer.");
      } else {
        console.log("La table promises existe déjà ou une autre erreur est survenue:", promisesError.message);
      }
    } else {
      console.log("La table promises existe et fonctionne correctement.");
    }

    // Créer la table advance_payments
    console.log("Création de la table advance_payments...");
    const { error: advancePaymentsError } = await supabase
      .from('advance_payments')
      .insert({ 
        sale_id: '00000000-0000-0000-0000-000000000000', 
        amount: 100,
        payment_date: new Date().toISOString().split('T')[0]
      })
      .select();

    if (advancePaymentsError) {
      if (advancePaymentsError.code === '42P01') {
        console.log("La table advance_payments n'existe pas encore. Nous allons la créer.");
      } else {
        console.log("La table advance_payments existe déjà ou une autre erreur est survenue:", advancePaymentsError.message);
      }
    } else {
      console.log("La table advance_payments existe et fonctionne correctement.");
    }

    console.log("Vérification des tables terminée. Veuillez consulter le fichier create-tables.sql pour créer les tables manquantes.");
  } catch (error) {
    console.error("Erreur lors de la vérification des tables:", error);
  }
}

// Exécuter la fonction de création des tables
createTables();
