import { Promise<PERSON><PERSON> } from "@/components/promises/PromiseChart";
import { PrintReport } from "@/components/reports/PrintReport";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useSupabasePromises } from "@/hooks/useSupabasePromises";
import { TelemarketerStats, TimePeriod } from "@/types";
import { Mail } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

export default function Promises() {
  const navigate = useNavigate();
  const { promises, isLoading, error, updatePromiseStatus, fulfillPromise } = useSupabasePromises();

  // Log promises data for debugging
  useEffect(() => {
    console.log("Promises data:", promises);
    if (error) {
      console.error("Error loading promises:", error);
      toast.error("Erreur lors du chargement des promesses");
    }
  }, [promises, error]);

  const [selectedTimePeriod, setSelectedTimePeriod] = useState<TimePeriod>("all");
  const [selectedTelemarketer, setSelectedTelemarketer] = useState<TelemarketerStats | null>(null);

  // Filter promises based on selected time period
  const filteredPromises = promises.filter(promise => {
    if (!promise) return false;

    // Filter by telemarketer if selected
    if (selectedTelemarketer && promise.telemarketer_id !== selectedTelemarketer.id) {
      return false;
    }

    // Filter by time period
    if (selectedTimePeriod !== "all") {
      const now = new Date();
      const dueDate = new Date(promise.due_date);

      if (selectedTimePeriod === "day") {
        return dueDate.toDateString() === now.toDateString();
      } else if (selectedTimePeriod === "week") {
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
        startOfWeek.setHours(0, 0, 0, 0);

        const endOfWeek = new Date(now);
        endOfWeek.setDate(now.getDate() + (6 - now.getDay())); // End of week (Saturday)
        endOfWeek.setHours(23, 59, 59, 999);

        return dueDate >= startOfWeek && dueDate <= endOfWeek;
      } else if (selectedTimePeriod === "month") {
        return dueDate.getMonth() === now.getMonth() && dueDate.getFullYear() === now.getFullYear();
      }
    }

    return true;
  });

  // Get unique telemarketers from promises
  const telemarketers = Array.from(
    new Map(
      promises
        .filter(p => p.telemarketers && p.telemarketers.name)
        .map(p => [p.telemarketer_id, {
          id: p.telemarketer_id,
          name: p.telemarketers.name,
          email: p.telemarketers.email
        }])
    ).values()
  );

  // Format the promises data for the report
  const reportData = filteredPromises.map(promise => ({
    id: promise.id,
    dueDate: promise.due_date,
    customerName: promise.customers.name,
    courseName: promise.courses.name,
    amount: promise.amount,
    telemarketerName: promise.telemarketers.name,
    status: promise.status,
    reminders: promise.reminders || 0
  }));

  // Helper function to get status badge
  const getStatusBadge = (status: string, source: 'promise' | 'sale') => {
    if (source === 'promise') {
      switch (status) {
        case 'pending':
          return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">En attente</Badge>;
        case 'postponed':
          return <Badge variant="outline" className="bg-blue-100 text-blue-800">Reportée</Badge>;
        case 'fulfilled':
          return <Badge variant="outline" className="bg-green-100 text-green-800">Réalisée</Badge>;
        case 'reminder-sent':
          return <Badge variant="outline" className="bg-purple-100 text-purple-800">Rappel envoyé</Badge>;
        default:
          return <Badge variant="outline">{status}</Badge>;
      }
    } else {
      // For sales with promise status
      return <Badge variant="outline" className="bg-indigo-100 text-indigo-800">Promesse</Badge>;
    }
  };

  // Send reminder function
  const sendReminder = (id: string, type: "email" | "phone", source: 'promise' | 'sale') => {
    const promise = promises.find(p => p.id === id);
    if (!promise) return;

    updatePromiseStatus(
      {
        id,
        status: "reminder-sent",
        source,
        reminders: (promise.reminders || 0) + 1
      },
      {
        onSuccess: () => {
          toast.success(`Rappel ${type === "email" ? "email" : "téléphonique"} envoyé au client ${promise.customers.name}`);
        },
        onError: (error) => {
          toast.error("Erreur lors de l'envoi du rappel");
          console.error("Error sending reminder:", error);
        }
      }
    );
  };

  // Postpone promise function
  const postponePromise = (id: string, days: number, source: 'promise' | 'sale') => {
    const promise = promises.find(p => p.id === id);
    if (!promise) return;

    const currentDueDate = new Date(promise.due_date);
    const newDueDate = new Date(currentDueDate);
    newDueDate.setDate(currentDueDate.getDate() + days);

    updatePromiseStatus(
      {
        id,
        status: "postponed",
        source,
        original_due_date: promise.original_due_date || promise.due_date,
        due_date: newDueDate.toISOString()
      },
      {
        onSuccess: () => {
          toast.success(`Promesse reportée de ${days} jours`);
        },
        onError: (error) => {
          toast.error("Erreur lors du report de la promesse");
          console.error("Error postponing promise:", error);
        }
      }
    );
  };

  // Mark promise as fulfilled and move to sales
  const handleFulfillPromise = (id: string, source: 'promise' | 'sale') => {
    fulfillPromise(
      { id, source },
      {
        onSuccess: () => {
          // In a real app, this would make an API call to add the promise to sales
          toast.success("Promesse réalisée et convertie en vente !");
          navigate('/sales');
        },
        onError: (error) => {
          toast.error("Erreur lors de la réalisation de la promesse");
          console.error("Error fulfilling promise:", error);
        }
      }
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Promesses de Paiement</h1>
        <div className="flex items-center gap-2">
          <Select
            value={selectedTimePeriod}
            onValueChange={(value) => setSelectedTimePeriod(value as TimePeriod)}
          >
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Période" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Aujourd'hui</SelectItem>
              <SelectItem value="week">Cette semaine</SelectItem>
              <SelectItem value="month">Ce mois</SelectItem>
              <SelectItem value="all">Tout</SelectItem>
            </SelectContent>
          </Select>

          <PrintReport
            type="promises"
            data={reportData}
            period={selectedTimePeriod}
          />
        </div>
      </div>

      {/* Only render chart if we have data */}
      {promises.length > 0 && (
        <PromiseChart promisesData={promises} />
      )}

      <Card>
        <CardHeader>
          <CardTitle>Promesses de Paiement</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date d'échéance</TableHead>
                <TableHead>Client</TableHead>
                <TableHead>Formation</TableHead>
                <TableHead>Montant</TableHead>
                <TableHead>Télévendeur</TableHead>
                <TableHead>Rappels</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredPromises.map((promise) => {
                const daysUntilDue = Math.ceil(
                  (new Date(promise.due_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
                );

                return (
                  <TableRow key={promise.id} className={
                    daysUntilDue < 0 ? "bg-red-50" :
                      daysUntilDue === 0 ? "bg-yellow-50" :
                        daysUntilDue <= 2 ? "bg-orange-50" : ""
                  }>
                    <TableCell>
                      {new Date(promise.due_date).toLocaleDateString("fr-FR")}
                      <div className="text-xs text-muted-foreground">
                        {daysUntilDue < 0
                          ? `En retard de ${Math.abs(daysUntilDue)} jour${Math.abs(daysUntilDue) > 1 ? 's' : ''}`
                          : daysUntilDue === 0
                            ? "Aujourd'hui"
                            : `Dans ${daysUntilDue} jour${daysUntilDue > 1 ? 's' : ''}`}
                      </div>
                    </TableCell>
                    <TableCell>
                      {promise.customers.name}
                      <div className="text-xs text-muted-foreground">
                        {promise.customers.phone} • {promise.customers.email}
                      </div>
                    </TableCell>
                    <TableCell>{promise.courses.name}</TableCell>
                    <TableCell>
                      {Number(promise.amount).toLocaleString("fr-FR")} XAF
                    </TableCell>
                    <TableCell>{promise.telemarketers.name}</TableCell>
                    <TableCell>{promise.reminders}</TableCell>
                    <TableCell>
                      <div className="flex flex-col space-y-2">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              // Ouvrir le client mail par défaut avec l'adresse email du client
                              if (promise.customers.email) {
                                const subject = `Rappel concernant votre promesse de paiement - ${promise.courses.name}`;
                                const body = `Bonjour ${promise.customers.name},\n\nNous vous rappelons votre engagement de paiement pour la formation "${promise.courses.name}" d'un montant de ${Number(promise.amount).toLocaleString("fr-FR")} XAF.\n\nDate d'échéance: ${new Date(promise.due_date).toLocaleDateString("fr-FR")}\n\nCordialement,\nL'équipe du Centre d'Appel Formation`;
                                window.location.href = `mailto:${promise.customers.email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

                                // Mettre à jour le statut comme un rappel envoyé
                                sendReminder(promise.id, "email", promise.source);
                              } else {
                                toast.error("Aucune adresse email disponible pour ce client");
                              }
                            }}
                            disabled={promise.status === "fulfilled" || !promise.customers.email}
                          >
                            <Mail className="h-4 w-4 mr-1" />
                            Envoyer Email
                          </Button>
                        </div>
                        <div className="flex space-x-2">
                          <Select
                            onValueChange={(value) => postponePromise(promise.id, parseInt(value), promise.source)}
                            disabled={promise.status === "fulfilled"}
                          >
                            <SelectTrigger className="h-8 text-xs">
                              <SelectValue placeholder="Reporter..." />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="7">Reporter 7 jours</SelectItem>
                              <SelectItem value="14">Reporter 14 jours</SelectItem>
                              <SelectItem value="30">Reporter 30 jours</SelectItem>
                            </SelectContent>
                          </Select>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleFulfillPromise(promise.id, promise.source)}
                            disabled={promise.status === "fulfilled"}
                          >
                            Réaliser
                          </Button>
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
              {filteredPromises.length === 0 && (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-4 text-muted-foreground italic">
                    Aucune promesse trouvée pour cette période
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
