import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSupabaseCourses } from "@/hooks/useSupabaseCourses";
import { useSupabaseCustomers } from "@/hooks/useSupabaseCustomers";
import { useSupabaseTelemarketers } from "@/hooks/useSupabaseTelemarketers";
import { TelemarketerStats } from "@/types";
import { BookPlus, PlusCircle, UserPlus } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { ComboboxSelect } from "./ComboboxSelect";
