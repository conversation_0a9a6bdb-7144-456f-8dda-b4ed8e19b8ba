// Script pour tester la récupération des rappels depuis le frontend
import { createClient } from '@supabase/supabase-js';

// Supabase connection details
const SUPABASE_URL = "https://vxzpeinztvurtncaaqdm.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ4enBlaW56dHZ1cnRuY2FhcWRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0ODY5MzksImV4cCI6MjA2MTA2MjkzOX0.EMJQUlI3yEwGzHCzuJcs8phfrEVDzJkavIdyKfbbk9s";

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

async function testFrontendRemindersQuery() {
  console.log("Test de la requête frontend pour les rappels...");

  try {
    // Reproduire exactement la requête du hook useSupabaseReminders
    const { data, error } = await supabase
      .from('reminders')
      .select(`
        *,
        customers:customer_id(*),
        telemarketers:telemarketer_id(*),
        courses:course_id(*)
      `)
      .order('due_date');

    if (error) {
      console.error("❌ Erreur lors de la récupération des rappels:", error);
    } else {
      console.log("✅ Rappels récupérés avec succès!");
      console.log("Nombre de rappels:", data?.length || 0);
      
      if (data && data.length > 0) {
        console.log("\nDétails des rappels:");
        data.forEach((reminder, index) => {
          console.log(`\n${index + 1}. Rappel ID: ${reminder.id}`);
          console.log(`   Client: ${reminder.customers?.name || 'N/A'}`);
          console.log(`   Télévendeur: ${reminder.telemarketers?.name || 'N/A'}`);
          console.log(`   Formation: ${reminder.courses?.name || 'N/A'}`);
          console.log(`   Montant: ${reminder.amount} XAF`);
          console.log(`   Date d'échéance: ${reminder.due_date}`);
          console.log(`   Statut: ${reminder.status}`);
        });
      }
    }

  } catch (error) {
    console.error("❌ Erreur fatale:", error);
  }
}

// Exécuter le test
testFrontendRemindersQuery()
  .then(() => {
    console.log("\nTest terminé");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Erreur fatale:", error);
    process.exit(1);
  });
