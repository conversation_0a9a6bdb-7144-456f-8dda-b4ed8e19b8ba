import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useSupabaseReminders } from "@/hooks/useSupabaseReminders";
import { TimePeriod } from "@/types";
import { Check, Phone } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

export default function Reminders() {
  const navigate = useNavigate();
  const { reminders, isLoading, error, updateReminderStatus, completeReminder } = useSupabaseReminders();

  // Log reminders data for debugging
  useEffect(() => {
    console.log("Reminders data:", reminders);
    console.log("Reminders loading state:", isLoading);
    console.log("Reminders error:", error);
    console.log("Reminders array length:", reminders?.length || 0);

    if (error) {
      console.error("Error loading reminders:", error);
      toast.error("Erreur lors du chargement des rappels");
    }
  }, [reminders, error, isLoading]);

  const [selectedTimePeriod, setSelectedTimePeriod] = useState<TimePeriod>("all");

  // Filter reminders based on selected time period
  const filteredReminders = reminders.filter(reminder => {
    if (!reminder) return false;

    // Filter by time period
    if (selectedTimePeriod !== "all") {
      const now = new Date();
      const dueDate = new Date(reminder.due_date);

      if (selectedTimePeriod === "day") {
        return dueDate.toDateString() === now.toDateString();
      } else if (selectedTimePeriod === "week") {
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
        startOfWeek.setHours(0, 0, 0, 0);

        const endOfWeek = new Date(now);
        endOfWeek.setDate(now.getDate() + (6 - now.getDay())); // End of week (Saturday)
        endOfWeek.setHours(23, 59, 59, 999);

        return dueDate >= startOfWeek && dueDate <= endOfWeek;
      } else if (selectedTimePeriod === "month") {
        return dueDate.getMonth() === now.getMonth() && dueDate.getFullYear() === now.getFullYear();
      }
    }

    return true;
  });

  // Debug filtered reminders
  useEffect(() => {
    console.log("Selected time period:", selectedTimePeriod);
    console.log("Filtered reminders:", filteredReminders);
    console.log("Filtered reminders length:", filteredReminders.length);
  }, [filteredReminders, selectedTimePeriod]);

  // Helper function to get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">En attente</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-100 text-green-800">Complété</Badge>;
      case 'cancelled':
        return <Badge variant="outline" className="bg-red-100 text-red-800">Annulé</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Mark reminder as completed
  const handleCompleteReminder = (id: string, createSale: boolean) => {
    completeReminder(
      { id, createSale },
      {
        onSuccess: () => {
          toast.success(createSale
            ? "Rappel complété et converti en vente !"
            : "Rappel marqué comme complété");

          if (createSale) {
            navigate('/sales');
          }
        },
        onError: (error) => {
          toast.error("Erreur lors de la complétion du rappel");
          console.error("Error completing reminder:", error);
        }
      }
    );
  };

  // Cancel reminder
  const handleCancelReminder = (id: string) => {
    updateReminderStatus(
      { id, status: "cancelled" },
      {
        onSuccess: () => {
          toast.success("Rappel annulé");
        },
        onError: (error) => {
          toast.error("Erreur lors de l'annulation du rappel");
          console.error("Error cancelling reminder:", error);
        }
      }
    );
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Rappels</h1>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Rappels Planifiés</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center py-8">
              <div className="text-muted-foreground">Chargement des rappels...</div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Rappels</h1>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Rappels Planifiés</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center py-8">
              <div className="text-red-500">
                Erreur lors du chargement des rappels. Veuillez rafraîchir la page.
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Rappels</h1>
        <div className="flex items-center gap-2">
          <Select
            value={selectedTimePeriod}
            onValueChange={(value) => setSelectedTimePeriod(value as TimePeriod)}
          >
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Période" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Aujourd'hui</SelectItem>
              <SelectItem value="week">Cette semaine</SelectItem>
              <SelectItem value="month">Ce mois</SelectItem>
              <SelectItem value="all">Tout</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Rappels Planifiés</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Client</TableHead>
                <TableHead>Formation</TableHead>
                <TableHead>Montant</TableHead>
                <TableHead>Télévendeur</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredReminders.map((reminder) => {
                const daysUntilDue = Math.ceil(
                  (new Date(reminder.due_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
                );

                return (
                  <TableRow key={reminder.id} className={
                    reminder.status !== 'pending' ? "" :
                      daysUntilDue < 0 ? "bg-red-50" :
                        daysUntilDue === 0 ? "bg-yellow-50" :
                          daysUntilDue <= 2 ? "bg-orange-50" : ""
                  }>
                    <TableCell>
                      {new Date(reminder.due_date).toLocaleDateString("fr-FR")}
                      {reminder.status === 'pending' && (
                        <div className="text-xs text-muted-foreground">
                          {daysUntilDue < 0
                            ? `En retard de ${Math.abs(daysUntilDue)} jour${Math.abs(daysUntilDue) > 1 ? 's' : ''}`
                            : daysUntilDue === 0
                              ? "Aujourd'hui"
                              : `Dans ${daysUntilDue} jour${daysUntilDue > 1 ? 's' : ''}`}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {reminder.customers.name}
                      <div className="text-xs text-muted-foreground">
                        {reminder.customers.phone} • {reminder.customers.email}
                      </div>
                    </TableCell>
                    <TableCell>{reminder.courses.name}</TableCell>
                    <TableCell>
                      {Number(reminder.amount).toLocaleString("fr-FR")} XAF
                    </TableCell>
                    <TableCell>{reminder.telemarketers.name}</TableCell>
                    <TableCell>{getStatusBadge(reminder.status)}</TableCell>
                    <TableCell>
                      {reminder.status === 'pending' && (
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleCompleteReminder(reminder.id, false)}
                          >
                            <Check className="h-4 w-4 mr-1" />
                            Compléter
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleCompleteReminder(reminder.id, true)}
                          >
                            <Phone className="h-4 w-4 mr-1" />
                            Vente
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-red-500 hover:text-red-700"
                            onClick={() => handleCancelReminder(reminder.id)}
                          >
                            Annuler
                          </Button>
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
              {filteredReminders.length === 0 && (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-4 text-muted-foreground italic">
                    Aucun rappel trouvé pour cette période
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
