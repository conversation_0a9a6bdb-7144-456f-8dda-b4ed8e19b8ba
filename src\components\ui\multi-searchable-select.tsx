import * as React from "react";
import { X, Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";

export interface MultiSearchableSelectOption {
  label: string;
  value: string;
}

interface MultiSearchableSelectProps {
  options: MultiSearchableSelectOption[];
  values: string[];
  onValuesChange: (values: string[]) => void;
  placeholder: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  className?: string;
  maxHeight?: string;
}

export function MultiSearchableSelect({
  options = [],
  values = [],
  onValuesChange,
  placeholder = "Sélectionner des options...",
  searchPlaceholder = "Rechercher...",
  emptyMessage = "Aucun résultat trouvé.",
  className,
  maxHeight = "200px",
}: MultiSearchableSelectProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");

  // Ensure options is always a valid array of valid objects
  const validOptions = React.useMemo(() => {
    if (!Array.isArray(options)) return [];
    
    return options.filter(option => 
      option && 
      typeof option === 'object' && 
      'label' in option &&
      'value' in option &&
      typeof option.label === 'string' && 
      typeof option.value === 'string'
    );
  }, [options]);

  // Get selected labels for display
  const selectedLabels = React.useMemo(() => {
    return values
      .map(value => {
        const option = validOptions.find(opt => opt.value === value);
        return option ? option.label : null;
      })
      .filter(Boolean) as string[];
  }, [values, validOptions]);

  // Filter options based on search query
  const filteredOptions = React.useMemo(() => {
    if (!searchQuery || !searchQuery.trim()) return validOptions;
    
    return validOptions.filter(option => 
      option.label.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [validOptions, searchQuery]);

  // Handle selection/deselection of an option
  const handleToggleOption = React.useCallback((value: string) => {
    if (typeof onValuesChange !== 'function') {
      console.warn("MultiSearchableSelect: onValuesChange is not a function");
      return;
    }
    
    const newValues = values.includes(value)
      ? values.filter(v => v !== value)
      : [...values, value];
    
    onValuesChange(newValues);
  }, [values, onValuesChange]);

  // Remove a selected value
  const handleRemoveValue = React.useCallback((value: string) => {
    if (typeof onValuesChange !== 'function') return;
    onValuesChange(values.filter(v => v !== value));
  }, [values, onValuesChange]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between", className)}
          type="button"
        >
          <div className="flex flex-wrap gap-1 items-center max-w-[90%] overflow-hidden">
            {selectedLabels.length > 0 ? (
              selectedLabels.map((label, i) => (
                <Badge 
                  key={i} 
                  variant="secondary"
                  className="mr-1 px-1.5 py-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    const value = values[i];
                    if (value) handleRemoveValue(value);
                  }}
                >
                  {label}
                  <X className="ml-1 h-3 w-3" />
                </Badge>
              ))
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
          </div>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <div className="flex flex-col">
          <div className="flex items-center border-b px-3">
            <Input
              placeholder={searchPlaceholder}
              className="h-9 border-none focus-visible:ring-0 focus-visible:ring-offset-0"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <ScrollArea className={`max-h-[${maxHeight}]`}>
            <div className="p-1">
              {filteredOptions.length > 0 ? (
                filteredOptions.map((option) => {
                  const isSelected = values.includes(option.value);
                  return (
                    <div
                      key={option.value}
                      className={cn(
                        "relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground",
                        isSelected && "bg-accent text-accent-foreground"
                      )}
                      onClick={() => handleToggleOption(option.value)}
                    >
                      {option.label}
                      {isSelected && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </div>
                  );
                })
              ) : (
                <div className="py-6 text-center text-sm">
                  {searchQuery ? emptyMessage : "Commencer à taper pour rechercher..."}
                </div>
              )}
              {validOptions.length === 0 && (
                <div className="py-6 text-center text-sm">Aucune option disponible</div>
              )}
            </div>
          </ScrollArea>
        </div>
      </PopoverContent>
    </Popover>
  );
}
