
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export type Sale = Database["public"]["Tables"]["sales"]["Row"];

export type SaleWithRelations = Sale & {
  customers: {
    name: string;
    email: string | null;
    phone: string | null;
  };
  telemarketers: {
    name: string;
    email: string | null;
  };
  courses: {
    name: string;
  };
  sale_courses?: {
    id: string;
    course_id: string;
    price: number;
    original_price: number;
    discount: number;
    courses: {
      name: string;
    };
  }[];
  advances?: {
    id: string;
    amount: number;
    payment_date: string;
  }[];
};

export function useSupabaseSales() {
  const queryClient = useQueryClient();

  const { data: sales, isLoading } = useQuery({
    queryKey: ['sales'],
    queryFn: async () => {
      console.log("Fetching sales data...");
      const { data: salesData, error } = await supabase
        .from('sales')
        .select(`
          *,
          customers:customer_id(*),
          telemarketers:telemarketer_id(*),
          courses:course_id(*),
          sale_courses(
            id,
            course_id,
            price,
            original_price,
            discount,
            courses:course_id(name)
          )
        `)
        .order('date', { ascending: false });

      if (error) {
        console.error("Error fetching sales:", error);
        throw error;
      }

      console.log("Sales data fetched:", salesData);

      // Get all advances for these sales
      const saleIds = salesData.map(sale => sale.id);
      if (saleIds.length > 0) {
        const { data: advances, error: advancesError } = await supabase
          .from('advance_payments')
          .select('*')
          .in('sale_id', saleIds);

        if (advancesError) {
          console.error("Error fetching advances:", advancesError);
        }

        if (!advancesError && advances) {
          console.log("Advances fetched:", advances);
          // Group advances by sale_id
          const advancesBySale = advances.reduce((acc, advance) => {
            if (!acc[advance.sale_id]) {
              acc[advance.sale_id] = [];
            }
            acc[advance.sale_id].push(advance);
            return acc;
          }, {} as Record<string, any[]>);

          // Add advances to each sale
          return salesData.map(sale => ({
            ...sale,
            advances: advancesBySale[sale.id] || []
          }));
        }
      }

      return salesData;
    }
  });

  const addSale = useMutation({
    mutationFn: async (sale: {
      customer_id: string;
      telemarketer_id: string;
      course_ids: string[];
      courses: { id: string; name: string; price: number }[];
      price: number;
      original_price?: number;
      discount?: number;
      status: "paid" | "advance" | "promise" | "reminder"; // Utiliser les valeurs exactes de l'enum
      date?: string;
      due_date?: string | null;
      advance_amount?: number | null;
      advance_due_date?: string | null;
    }) => {
      // Format the sale object before submitting
      const today = new Date().toISOString().split('T')[0]; // Format as YYYY-MM-DD

      // Utiliser le premier cours comme cours principal pour la table sales
      // (nécessaire pour maintenir la compatibilité avec la structure existante)
      const primaryCourse = sale.courses[0];

      const formattedSale = {
        customer_id: sale.customer_id,
        telemarketer_id: sale.telemarketer_id,
        course_id: primaryCourse.id, // Utiliser le premier cours comme cours principal
        price: sale.price,
        original_price: sale.original_price || sale.price,
        discount: sale.discount || 0,
        status: sale.status,
        date: sale.date ? new Date(sale.date).toISOString().split('T')[0] : today,
        due_date: sale.due_date || today, // due_date est NOT NULL dans la base de données
        advance_amount: sale.advance_amount || null,
        advance_due_date: sale.advance_due_date || null
      };

      console.log("Submitting sale:", formattedSale);

      try {
        // 1. Insérer la vente principale
        const { data, error } = await supabase
          .from('sales')
          .insert(formattedSale)
          .select()
          .single();

        if (error) {
          console.error("Supabase error:", error);
          throw error;
        }

        // 2. Insérer les relations entre la vente et les formations
        if (sale.courses.length > 0) {
          const saleCoursesData = sale.courses.map(course => ({
            sale_id: data.id,
            course_id: course.id,
            price: course.price,
            original_price: course.price,
            discount: 0
          }));

          const { error: saleCoursesError } = await supabase
            .from('sale_courses')
            .insert(saleCoursesData);

          if (saleCoursesError) {
            console.error("Error adding sale_courses:", saleCoursesError);
            // Ne pas échouer la transaction principale si l'ajout des formations échoue
          }
        }

        // Si c'est une vente avec avance, ajouter automatiquement le paiement d'avance
        if (formattedSale.status === 'advance' && formattedSale.advance_amount) {
          try {
            const { error: advanceError } = await supabase
              .from('advance_payments')
              .insert({
                sale_id: data.id,
                amount: formattedSale.advance_amount,
                payment_date: new Date().toISOString().split('T')[0] // Format as YYYY-MM-DD
              });

            if (advanceError) {
              console.error("Error adding initial advance payment:", advanceError);
              // Ne pas échouer la transaction principale si l'ajout de l'avance échoue
            }
          } catch (advanceErr) {
            console.error("Exception adding initial advance payment:", advanceErr);
          }
        }

        console.log("Sale added successfully:", data);

        return data;

      } catch (error) {
        console.error("Error in addSale:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sales'] });
    }
  });

  const updateSaleStatus = useMutation({
    mutationFn: async ({ id, status, due_date }: { id: string; status: "paid" | "advance" | "promise" | "reminder"; due_date?: string | null }) => {
      try {
        const today = new Date().toISOString().split('T')[0]; // Format as YYYY-MM-DD

        const updateData: Partial<Sale> = {
          status,
          // Format due_date as YYYY-MM-DD if provided, otherwise use current date
          due_date: due_date || today
        };

        console.log("Updating sale status:", id, updateData);

        const { data, error } = await supabase
          .from('sales')
          .update(updateData)
          .eq('id', id)
          .select()
          .single();

        if (error) {
          console.error("Error updating sale status:", error);
          throw error;
        }
        return data;
      } catch (error) {
        console.error("Error in updateSaleStatus:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sales'] });
    }
  });

  const addAdvancePayment = useMutation({
    mutationFn: async ({ sale_id, amount }: { sale_id: string; amount: number }) => {
      console.log("Adding advance payment:", sale_id, amount);

      // First update the sale status to "advance" if it's not already
      const { data: saleData, error: saleError } = await supabase
        .from('sales')
        .select('status')
        .eq('id', sale_id)
        .single();

      if (saleError) {
        console.error("Error fetching sale:", saleError);
        throw saleError;
      }

      if (saleData.status !== 'advance') {
        const { error: updateError } = await supabase
          .from('sales')
          .update({ status: 'advance' })
          .eq('id', sale_id);

        if (updateError) {
          console.error("Error updating sale status:", updateError);
          throw updateError;
        }
      }

      // Then add the advance payment
      const { data, error } = await supabase
        .from('advance_payments')
        .insert({
          sale_id,
          amount,
          payment_date: new Date().toISOString().split('T')[0] // Format as YYYY-MM-DD
        })
        .select()
        .single();

      if (error) {
        console.error("Error adding advance payment:", error);
        throw error;
      }
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sales'] });
    }
  });

  const updateSale = useMutation({
    mutationFn: async (sale: {
      id: string;
      customer_id: string;
      telemarketer_id: string;
      course_ids: string[];
      courses: { id: string; name: string; price: number }[];
      price: number;
      original_price?: number;
      discount?: number;
      status: "paid" | "advance" | "promise" | "reminder";
      date?: string;
      due_date?: string | null;
      advance_amount?: number | null;
      advance_due_date?: string | null;
    }) => {
      const today = new Date().toISOString().split('T')[0]; // Format as YYYY-MM-DD

      // Utiliser le premier cours comme cours principal pour la table sales
      const primaryCourse = sale.courses[0];

      const formattedSale = {
        customer_id: sale.customer_id,
        telemarketer_id: sale.telemarketer_id,
        course_id: primaryCourse.id,
        price: sale.price,
        original_price: sale.original_price || sale.price,
        discount: sale.discount || 0,
        status: sale.status,
        date: sale.date ? new Date(sale.date).toISOString().split('T')[0] : today,
        due_date: sale.due_date || today,
        advance_amount: sale.advance_amount || null,
        advance_due_date: sale.advance_due_date || null
      };

      console.log("Updating sale:", sale.id, formattedSale);

      try {
        // 1. Mettre à jour la vente principale
        const { data, error } = await supabase
          .from('sales')
          .update(formattedSale)
          .eq('id', sale.id)
          .select()
          .single();

        if (error) {
          console.error("Supabase error:", error);
          throw error;
        }

        // 2. Supprimer les anciennes relations entre la vente et les formations
        const { error: deleteError } = await supabase
          .from('sale_courses')
          .delete()
          .eq('sale_id', sale.id);

        if (deleteError) {
          console.error("Error deleting old sale_courses:", deleteError);
          // Ne pas échouer la transaction principale si la suppression échoue
        }

        // 3. Insérer les nouvelles relations entre la vente et les formations
        if (sale.courses.length > 0) {
          const saleCoursesData = sale.courses.map(course => ({
            sale_id: sale.id,
            course_id: course.id,
            price: course.price,
            original_price: course.price,
            discount: 0
          }));

          const { error: saleCoursesError } = await supabase
            .from('sale_courses')
            .insert(saleCoursesData);

          if (saleCoursesError) {
            console.error("Error adding sale_courses:", saleCoursesError);
            // Ne pas échouer la transaction principale si l'ajout des formations échoue
          }
        }

        console.log("Sale updated successfully:", data);
        return data;
      } catch (error) {
        console.error("Error in updateSale:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sales'] });
    }
  });

  return {
    sales: sales as SaleWithRelations[] || [],
    isLoading,
    addSale: addSale.mutate,
    updateSale: updateSale.mutate,
    updateSaleStatus: updateSaleStatus.mutate,
    addAdvancePayment: addAdvancePayment.mutate
  };
}
