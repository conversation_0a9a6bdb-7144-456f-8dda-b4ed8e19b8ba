import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";

export type Reminder = {
  id: string;
  customer_id: string;
  telemarketer_id: string;
  course_id: string;
  amount: number;
  status: Database["public"]["Enums"]["reminder_status"];
  due_date: string;
  created_at: string;
};

export type ReminderWithRelations = Reminder & {
  customers: {
    name: string;
    email: string | null;
    phone: string | null;
  };
  telemarketers: {
    name: string;
    email: string | null;
  };
  courses: {
    name: string;
  };
};

export function useSupabaseReminders() {
  const queryClient = useQueryClient();

  const { data: reminders, isLoading, error } = useQuery({
    queryKey: ['reminders'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('reminders')
        .select(`
          *,
          customers:customer_id(*),
          telemarketers:telemarketer_id(*),
          courses:course_id(*)
        `)
        .order('due_date');

      if (error) {
        console.error("Error fetching reminders:", error);
        throw error;
      }

      return data as ReminderWithRelations[];
    }
  });

  const addReminder = useMutation({
    mutationFn: async (reminder: {
      customer_id: string;
      telemarketer_id: string;
      course_id: string;
      amount: number;
      due_date: string;
    }) => {
      const { data, error } = await supabase
        .from('reminders')
        .insert({
          ...reminder,
          status: 'pending' as Database["public"]["Enums"]["reminder_status"]
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reminders'] });
    }
  });

  const updateReminderStatus = useMutation({
    mutationFn: async ({ id, status }: { id: string; status: Database["public"]["Enums"]["reminder_status"] }) => {
      const { data, error } = await supabase
        .from('reminders')
        .update({ status })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reminders'] });
    }
  });

  const completeReminder = useMutation({
    mutationFn: async ({ id, createSale = false }: { id: string; createSale?: boolean }) => {
      try {
        // First, get the reminder details
        const { data: reminder, error: getError } = await supabase
          .from('reminders')
          .select('*')
          .eq('id', id)
          .single();

        if (getError) throw getError;

        // Update the reminder status to completed
        const { data, error: updateError } = await supabase
          .from('reminders')
          .update({ status: 'completed' })
          .eq('id', id)
          .select();

        if (updateError) throw updateError;

        // If createSale is true, create a new sale record
        if (createSale) {
          const { error: saleError } = await supabase
            .from('sales')
            .insert({
              customer_id: reminder.customer_id,
              telemarketer_id: reminder.telemarketer_id,
              course_id: reminder.course_id,
              price: reminder.amount,
              status: 'paid',
              date: new Date().toISOString(),
              due_date: new Date().toISOString()
            });

          if (saleError) throw saleError;
        }

        return data;
      } catch (err) {
        console.error("Error completing reminder:", err);
        throw err;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reminders'] });
      queryClient.invalidateQueries({ queryKey: ['sales'] });
    }
  });

  return {
    reminders: reminders || [],
    isLoading,
    error,
    addReminder: addReminder.mutate,
    updateReminderStatus: updateReminderStatus.mutate,
    completeReminder: completeReminder.mutate
  };
}
