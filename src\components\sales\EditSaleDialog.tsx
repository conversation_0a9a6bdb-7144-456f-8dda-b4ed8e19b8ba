import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSupabaseCourses } from "@/hooks/useSupabaseCourses";
import { useSupabaseCustomers } from "@/hooks/useSupabaseCustomers";
import { SaleWithRelations } from "@/hooks/useSupabaseSales";
import { useSupabaseTelemarketers } from "@/hooks/useSupabaseTelemarketers";
import { TelemarketerStats } from "@/types";
import { Edit } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { ComboboxSelect } from "./ComboboxSelect";
import { MultiComboboxSelect } from "./MultiComboboxSelect";

interface Course {
  id: string;
  name: string;
  price: number;
}

interface Customer {
  id: string;
  name: string;
  email?: string | null;
  phone?: string | null;
}

interface EditSaleDialogProps {
  sale: SaleWithRelations;
  onSaleUpdate: (sale: any) => void;
  telemarketers: TelemarketerStats[];
  courses: Course[];
  customers: Customer[];
}

export function EditSaleDialog({ sale, onSaleUpdate, telemarketers = [], courses = [], customers = [] }: EditSaleDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [editedSale, setEditedSale] = useState({
    id: sale.id,
    customer_id: sale.customer_id,
    telemarketer_id: sale.telemarketer_id,
    course_ids: [] as string[],
    price: sale.price.toString(),
    original_price: sale.original_price?.toString() || sale.price.toString(),
    discount: sale.discount || 0,
    status: sale.status as "paid" | "advance" | "promise" | "reminder",
    date: sale.date,
    due_date: sale.due_date || "",
    advance_amount: sale.advance_amount?.toString() || "",
    advance_due_date: sale.advance_due_date || ""
  });

  const [showCustomerForm, setShowCustomerForm] = useState(false);
  const [showTelemarketerForm, setShowTelemarketerForm] = useState(false);
  const [showCourseForm, setShowCourseForm] = useState(false);

  const [newCustomer, setNewCustomer] = useState({
    name: "",
    email: "",
    phone: "",
  });

  const [newTelemarketer, setNewTelemarketer] = useState({
    name: "",
    email: "",
    phone: "",
    team_leader_id: ""
  });

  const [newCourse, setNewCourse] = useState({
    name: "",
    price: ""
  });

  const [validationErrors, setValidationErrors] = useState({
    customer: false,
    telemarketer: false,
    course: false
  });

  const { addCustomer } = useSupabaseCustomers();
  const { addTelemarketer } = useSupabaseTelemarketers();
  const { addCourse } = useSupabaseCourses();

  // Ensure proper data type validation for options
  const safeCustomers = Array.isArray(customers) ? customers : [];
  const safeTelemarketers = Array.isArray(telemarketers) ? telemarketers : [];
  const safeCourses = Array.isArray(courses) ? courses : [];

  const customerOptions = safeCustomers
    .filter(c => c && typeof c === 'object' && c.id && typeof c.name === 'string')
    .map(c => ({ label: c.name, value: c.id }));

  const telemarketerOptions = safeTelemarketers
    .filter(t => t && typeof t === 'object' && t.id && typeof t.name === 'string' && !t.archived)
    .map(t => ({ label: t.name, value: t.id }));

  const courseOptions = safeCourses
    .filter(c => c && typeof c === 'object' && c.id && typeof c.name === 'string')
    .map(c => ({ label: c.name, value: c.id }));

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString("fr-FR", { style: "currency", currency: "XOF" });
  };

  // Initialiser les formations sélectionnées
  useEffect(() => {
    if (isOpen) {
      // Si la vente a des sale_courses, les utiliser
      if (sale.sale_courses && sale.sale_courses.length > 0) {
        const courseIds = sale.sale_courses.map(sc => sc.course_id);
        setEditedSale(prev => ({
          ...prev,
          course_ids: courseIds
        }));
      } else {
        // Sinon, utiliser le course_id principal
        setEditedSale(prev => ({
          ...prev,
          course_ids: [sale.course_id]
        }));
      }
    }
  }, [isOpen, sale]);

  const handleCoursesSelect = (courseIds: string[]) => {
    if (!courseIds.length) {
      setEditedSale({
        ...editedSale,
        course_ids: [],
        price: "",
        original_price: "",
        discount: 0
      });
      return;
    }

    // Calculer le prix total des formations sélectionnées
    let totalPrice = 0;
    courseIds.forEach(courseId => {
      const course = safeCourses.find(c => c && c.id === courseId);
      if (course) {
        totalPrice += course.price;
      }
    });

    setEditedSale({
      ...editedSale,
      course_ids: courseIds,
      price: totalPrice.toString(),
      original_price: totalPrice.toString(),
      discount: 0
    });

    setValidationErrors(prev => ({ ...prev, course: false }));
  };

  const handleDiscountChange = (discountPercent: number) => {
    if (discountPercent < 0 || discountPercent > 100) return;

    const originalPrice = parseFloat(editedSale.original_price);
    if (isNaN(originalPrice)) return;

    const discountAmount = originalPrice * (discountPercent / 100);
    const finalPrice = originalPrice - discountAmount;

    setEditedSale({
      ...editedSale,
      discount: discountPercent,
      price: finalPrice.toString()
    });
  };

  const handleUpdateSale = () => {
    const errors = {
      customer: !editedSale.customer_id,
      telemarketer: !editedSale.telemarketer_id,
      course: !editedSale.course_ids.length
    };

    setValidationErrors(errors);

    if (errors.customer || errors.telemarketer || errors.course) {
      if (errors.customer) toast.error("Veuillez sélectionner un client");
      if (errors.telemarketer) toast.error("Veuillez sélectionner un télévendeur");
      if (errors.course) toast.error("Veuillez sélectionner au moins une formation");
      return;
    }

    if (isNaN(parseFloat(editedSale.price)) || parseFloat(editedSale.price) <= 0) {
      toast.error("Veuillez saisir un prix valide");
      return;
    }

    // Vérifier si une date d'échéance est requise mais non fournie
    if ((editedSale.status === 'promise' || editedSale.status === 'reminder') && !editedSale.due_date) {
      toast.error("Veuillez saisir une date d'échéance");
      return;
    }

    // Vérifier si une date limite de paiement est requise pour les avances
    if (editedSale.status === 'advance' && !editedSale.advance_due_date) {
      toast.error("Veuillez saisir une date limite de paiement");
      return;
    }

    // Vérifier si un montant d'avance est requis pour les avances
    if (editedSale.status === 'advance' && (!editedSale.advance_amount || parseFloat(editedSale.advance_amount) <= 0)) {
      toast.error("Veuillez saisir un montant d'avance valide");
      return;
    }

    const today = new Date().toISOString().split('T')[0]; // Format as YYYY-MM-DD

    // Récupérer les détails des formations sélectionnées
    const selectedCourses = editedSale.course_ids.map(courseId => {
      const course = safeCourses.find(c => c.id === courseId);
      return {
        id: courseId,
        name: course?.name || "",
        price: course?.price || 0
      };
    });

    const updatedSale = {
      ...editedSale,
      id: sale.id,
      price: parseFloat(editedSale.price),
      original_price: parseFloat(editedSale.original_price),
      discount: editedSale.discount,
      date: editedSale.date || today,
      // La date d'échéance est obligatoire dans la base de données
      due_date: editedSale.status === 'advance'
        ? editedSale.advance_due_date // Pour les avances, utiliser la date limite de paiement
        : (editedSale.status === 'promise' || editedSale.status === 'reminder')
          ? editedSale.due_date // Pour les promesses, utiliser la date d'échéance
          : today, // Utiliser la date du jour pour les ventes payées
      advance_amount: editedSale.status === 'advance' && editedSale.advance_amount
        ? parseFloat(editedSale.advance_amount)
        : null,
      advance_due_date: editedSale.status === 'advance' && editedSale.advance_due_date
        ? editedSale.advance_due_date // Already in YYYY-MM-DD format from input type="date"
        : null,
      courses: selectedCourses
    };

    if (typeof onSaleUpdate === 'function') {
      onSaleUpdate(updatedSale);
    }
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button size="sm" variant="outline">
          <Edit className="h-4 w-4 mr-1" />
          Modifier
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-[95vh] max-w-[600px] pb-4 overflow-hidden" onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogHeader className="pb-2">
          <DialogTitle>Modifier la vente</DialogTitle>
          <DialogDescription>
            Modifiez les informations de la vente
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col h-full max-h-[calc(95vh-10rem)] min-h-0">
          <ScrollArea className="flex-1 pr-4 overflow-y-auto">
            <div className="grid gap-3 py-2 px-1">
              <div className="grid gap-1">
                <Label>Client {validationErrors.customer && <span className="text-destructive">*</span>}</Label>
                <ComboboxSelect
                  options={customerOptions}
                  value={editedSale.customer_id}
                  onValueChange={(value) => {
                    setEditedSale({ ...editedSale, customer_id: value });
                    setValidationErrors(prev => ({ ...prev, customer: false }));
                  }}
                  placeholder="Sélectionnez un client"
                  searchPlaceholder="Rechercher un client..."
                />
              </div>

              <div className="grid gap-1">
                <Label>Télévendeur {validationErrors.telemarketer && <span className="text-destructive">*</span>}</Label>
                <ComboboxSelect
                  options={telemarketerOptions}
                  value={editedSale.telemarketer_id}
                  onValueChange={(value) => {
                    setEditedSale({ ...editedSale, telemarketer_id: value });
                    setValidationErrors(prev => ({ ...prev, telemarketer: false }));
                  }}
                  placeholder="Sélectionnez le télévendeur"
                  searchPlaceholder="Rechercher un télévendeur..."
                />
              </div>

              <div className="grid gap-1">
                <Label>Formation {validationErrors.course && <span className="text-destructive">*</span>}</Label>
                <MultiComboboxSelect
                  options={courseOptions}
                  values={editedSale.course_ids}
                  onValuesChange={handleCoursesSelect}
                  placeholder="Sélectionnez une ou plusieurs formations"
                  searchPlaceholder="Rechercher une formation..."
                />
              </div>

              <div className="grid gap-1">
                <Label>Prix original</Label>
                <Input
                  type="number"
                  value={editedSale.original_price}
                  onChange={(e) => setEditedSale({ ...editedSale, original_price: e.target.value })}
                  placeholder="0 XAF"
                />
                {editedSale.original_price && (
                  <div className="text-sm text-muted-foreground">
                    {formatCurrency(parseFloat(editedSale.original_price))}
                  </div>
                )}
              </div>

              <div className="grid gap-1">
                <Label>Réduction (%)</Label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  value={editedSale.discount}
                  onChange={(e) => handleDiscountChange(parseFloat(e.target.value) || 0)}
                  placeholder="0%"
                />
              </div>

              <div className="grid gap-1">
                <Label>Prix final</Label>
                <Input
                  type="number"
                  value={editedSale.price}
                  onChange={(e) => setEditedSale({ ...editedSale, price: e.target.value })}
                  placeholder="0 XAF"
                />
                {editedSale.price && (
                  <div className="text-sm text-muted-foreground">
                    {formatCurrency(parseFloat(editedSale.price))}
                    {editedSale.discount > 0 && (
                      <span className="ml-2 text-green-600">
                        (-{editedSale.discount}%)
                      </span>
                    )}
                  </div>
                )}
              </div>

              <div className="grid gap-1">
                <Label>Type d'enregistrement</Label>
                <Select
                  value={editedSale.status}
                  onValueChange={(value: "paid" | "advance" | "promise" | "reminder") => setEditedSale({ ...editedSale, status: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionnez le type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="paid">Payé</SelectItem>
                    <SelectItem value="advance">Avance</SelectItem>
                    <SelectItem value="promise">Promesse de paiement</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-1">
                <Label>Date de la vente</Label>
                <Input
                  type="date"
                  value={editedSale.date}
                  onChange={(e) => setEditedSale({ ...editedSale, date: e.target.value })}
                />
              </div>

              {(editedSale.status === "promise") && (
                <div className="grid gap-1">
                  <Label>Date d'échéance</Label>
                  <Input
                    type="date"
                    value={editedSale.due_date}
                    onChange={(e) => setEditedSale({ ...editedSale, due_date: e.target.value })}
                  />
                </div>
              )}

              {(editedSale.status === "advance") && (
                <>
                  <div className="grid gap-1">
                    <Label>Montant de l'avance</Label>
                    <Input
                      type="number"
                      min="0"
                      max={editedSale.price}
                      value={editedSale.advance_amount}
                      onChange={(e) => setEditedSale({ ...editedSale, advance_amount: e.target.value })}
                      placeholder="Montant de l'avance"
                    />
                    {editedSale.advance_amount && editedSale.price && (
                      <div className="text-sm text-muted-foreground">
                        {formatCurrency(parseFloat(editedSale.advance_amount))} sur {formatCurrency(parseFloat(editedSale.price))}
                        {" "}
                        ({Math.round((parseFloat(editedSale.advance_amount) / parseFloat(editedSale.price)) * 100)}%)
                      </div>
                    )}
                  </div>
                  <div className="grid gap-1">
                    <Label>Date limite de paiement</Label>
                    <Input
                      type="date"
                      value={editedSale.advance_due_date}
                      onChange={(e) => setEditedSale({ ...editedSale, advance_due_date: e.target.value })}
                    />
                  </div>
                </>
              )}
            </div>
          </ScrollArea>
        </div>
        <DialogFooter className="pt-2 mt-1">
          <Button onClick={handleUpdateSale}>
            Mettre à jour la vente
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}