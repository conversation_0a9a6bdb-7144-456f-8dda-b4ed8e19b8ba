// Script pour remplir la base de données avec des données de test pour un coaching en relation amoureuse
import { createClient } from '@supabase/supabase-js';

// Supabase connection details
const SUPABASE_URL = "https://vxzpeinztvurtncaaqdm.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ4enBlaW56dHZ1cnRuY2FhcWRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0ODY5MzksImV4cCI6MjA2MTA2MjkzOX0.EMJQUlI3yEwGzHCzuJcs8phfrEVDzJkavIdyKfbbk9s";

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// Données de test pour les coachs en relation amoureuse (télévendeurs)
const coaches = [
  { name: "<PERSON>", email: "<EMAIL>", phone: "237612345678" },
  { name: "<PERSON>", email: "<EMAIL>", phone: "237623456789" },
  { name: "<PERSON>", email: "<EMAIL>", phone: "237634567890" },
  { name: "<PERSON> Relation", email: "<EMAIL>", phone: "237645678901" },
  { name: "Nathalie Couple", email: "<EMAIL>", phone: "237656789012" }
];

// Données de test pour les programmes de coaching (formations)
const programs = [
  { name: "Séduction Avancée", price: 95000 },
  { name: "Préparation au Mariage", price: 150000 },
  { name: "Reconquérir son Ex", price: 120000 },
  { name: "Communication de Couple", price: 85000 },
  { name: "Fiançailles Réussies", price: 110000 },
  { name: "Premier Rendez-vous Parfait", price: 65000 },
  { name: "Surmonter une Rupture", price: 90000 }
];

// Données de test pour les clients
const clients = [
  { name: "Jean Célibataire", email: "<EMAIL>", phone: "237667890123" },
  { name: "Marie Divorcée", email: "<EMAIL>", phone: "237678901234" },
  { name: "Pierre et Julie", email: "<EMAIL>", phone: "237689012345" },
  { name: "Sophie Fiancée", email: "<EMAIL>", phone: "237690123456" },
  { name: "Lucas Timide", email: "<EMAIL>", phone: "237601234567" },
  { name: "Emma Amoureuse", email: "<EMAIL>", phone: "237612345678" },
  { name: "Thomas et Sarah", email: "<EMAIL>", phone: "237623456789" },
  { name: "Camille Célibataire", email: "<EMAIL>", phone: "237634567890" },
  { name: "Hugo Séducteur", email: "<EMAIL>", phone: "237645678901" },
  { name: "Léa Mariée", email: "<EMAIL>", phone: "237656789012" },
  { name: "Antoine Rupture", email: "<EMAIL>", phone: "237667890123" },
  { name: "Chloé Relation", email: "<EMAIL>", phone: "237678901234" }
];

// Fonction pour générer une date aléatoire dans les 30 derniers jours
function getRandomDate(daysBack = 30) {
  const today = new Date();
  const pastDate = new Date(today);
  pastDate.setDate(today.getDate() - Math.floor(Math.random() * daysBack));
  return pastDate.toISOString().split('T')[0];
}

// Fonction pour générer une date future aléatoire dans les 30 prochains jours
function getRandomFutureDate(daysAhead = 30) {
  const today = new Date();
  const futureDate = new Date(today);
  futureDate.setDate(today.getDate() + Math.floor(Math.random() * daysAhead) + 1);
  return futureDate.toISOString().split('T')[0];
}

// Fonction pour générer un nombre aléatoire entre min et max
function getRandomNumber(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Fonction pour insérer les données dans la base de données
async function seedDatabase() {
  console.log("Début de l'insertion des données...");

  // Insérer les coachs (télévendeurs)
  console.log("Insertion des coachs en relation amoureuse...");
  const { data: insertedCoaches, error: coachesError } = await supabase
    .from('telemarketers')
    .insert(coaches)
    .select();

  if (coachesError) {
    console.error("Erreur lors de l'insertion des coachs:", coachesError);
    return;
  }

  console.log(`${insertedCoaches.length} coachs insérés.`);

  // Insérer les programmes (formations)
  console.log("Insertion des programmes de coaching...");
  const { data: insertedPrograms, error: programsError } = await supabase
    .from('courses')
    .insert(programs)
    .select();

  if (programsError) {
    console.error("Erreur lors de l'insertion des programmes:", programsError);
    return;
  }

  console.log(`${insertedPrograms.length} programmes insérés.`);

  // Insérer les clients
  console.log("Insertion des clients...");
  const { data: insertedClients, error: clientsError } = await supabase
    .from('customers')
    .insert(clients)
    .select();

  if (clientsError) {
    console.error("Erreur lors de l'insertion des clients:", clientsError);
    return;
  }

  console.log(`${insertedClients.length} clients insérés.`);

  // Générer et insérer des ventes
  const sales = [];
  const statuses = ['paid', 'advance', 'promise'];

  // Générer 40 ventes
  for (let i = 0; i < 40; i++) {
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const program = insertedPrograms[Math.floor(Math.random() * insertedPrograms.length)];
    const price = program.price;

    const sale = {
      customer_id: insertedClients[Math.floor(Math.random() * insertedClients.length)].id,
      telemarketer_id: insertedCoaches[Math.floor(Math.random() * insertedCoaches.length)].id,
      course_id: program.id,
      date: getRandomDate(),
      price: price,
      status: status,
      due_date: getRandomFutureDate() // Toutes les ventes ont besoin d'une date d'échéance
    };

    sales.push(sale);
  }

  // Insérer les ventes
  console.log("Insertion des ventes de coaching...");
  const { data: insertedSales, error: salesError } = await supabase
    .from('sales')
    .insert(sales)
    .select();

  if (salesError) {
    console.error("Erreur lors de l'insertion des ventes:", salesError);
    return;
  }

  console.log(`${insertedSales.length} ventes de coaching insérées.`);

  console.log("Insertion des données terminée avec succès!");
}

// Exécuter la fonction d'insertion
seedDatabase();
