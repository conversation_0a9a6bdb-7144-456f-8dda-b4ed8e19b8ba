// Script pour créer les tables dans la base de données Supabase
import { createClient } from '@supabase/supabase-js';

// Supabase connection details
const SUPABASE_URL = "https://vxzpeinztvurtncaaqdm.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ4enBlaW56dHZ1cnRuY2FhcWRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0ODY5MzksImV4cCI6MjA2MTA2MjkzOX0.EMJQUlI3yEwGzHCzuJcs8phfrEVDzJkavIdyKfbbk9s";

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// Fonction pour créer les tables
async function createTables() {
  console.log("Début de la création des tables...");

  try {
    // 1. Créer les types énumérés
    console.log("Création des types énumérés...");
    
    // promise_status
    const { error: promiseStatusError } = await supabase.rpc('create_enum_type', {
      type_name: 'promise_status',
      values: ['pending', 'postponed', 'fulfilled', 'reminder-sent']
    });
    
    if (promiseStatusError) {
      console.log("Type promise_status peut déjà exister:", promiseStatusError.message);
    } else {
      console.log("Type promise_status créé avec succès");
    }
    
    // reminder_status
    const { error: reminderStatusError } = await supabase.rpc('create_enum_type', {
      type_name: 'reminder_status',
      values: ['pending', 'completed', 'cancelled']
    });
    
    if (reminderStatusError) {
      console.log("Type reminder_status peut déjà exister:", reminderStatusError.message);
    } else {
      console.log("Type reminder_status créé avec succès");
    }
    
    // sale_status
    const { error: saleStatusError } = await supabase.rpc('create_enum_type', {
      type_name: 'sale_status',
      values: ['paid', 'advance', 'promise', 'reminder']
    });
    
    if (saleStatusError) {
      console.log("Type sale_status peut déjà exister:", saleStatusError.message);
    } else {
      console.log("Type sale_status créé avec succès");
    }

    // 2. Créer la table customers
    console.log("Création de la table customers...");
    const { error: customersError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'customers',
      columns: [
        'id uuid primary key default uuid_generate_v4()',
        'name text not null',
        'email text',
        'phone text',
        'created_at timestamp with time zone default now()'
      ]
    });
    
    if (customersError) {
      console.error("Erreur lors de la création de la table customers:", customersError);
    } else {
      console.log("Table customers créée avec succès");
    }

    // 3. Créer la table telemarketers
    console.log("Création de la table telemarketers...");
    const { error: telemarketerError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'telemarketers',
      columns: [
        'id uuid primary key default uuid_generate_v4()',
        'name text not null',
        'email text',
        'phone text',
        'archived boolean default false',
        'team_leader_id uuid references telemarketers(id)',
        'created_at timestamp with time zone default now()'
      ]
    });
    
    if (telemarketerError) {
      console.error("Erreur lors de la création de la table telemarketers:", telemarketerError);
    } else {
      console.log("Table telemarketers créée avec succès");
    }

    // 4. Créer la table courses
    console.log("Création de la table courses...");
    const { error: coursesError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'courses',
      columns: [
        'id uuid primary key default uuid_generate_v4()',
        'name text not null',
        'price numeric not null',
        'created_at timestamp with time zone default now()'
      ]
    });
    
    if (coursesError) {
      console.error("Erreur lors de la création de la table courses:", coursesError);
    } else {
      console.log("Table courses créée avec succès");
    }

    // 5. Créer la table sales
    console.log("Création de la table sales...");
    const { error: salesError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'sales',
      columns: [
        'id uuid primary key default uuid_generate_v4()',
        'customer_id uuid not null references customers(id)',
        'telemarketer_id uuid not null references telemarketers(id)',
        'course_id uuid not null references courses(id)',
        'date date not null',
        'price numeric not null',
        'original_price numeric',
        'discount numeric default 0',
        'status text not null default \'paid\'',
        'due_date date',
        'advance_amount numeric',
        'advance_due_date date',
        'created_at timestamp with time zone default now()'
      ]
    });
    
    if (salesError) {
      console.error("Erreur lors de la création de la table sales:", salesError);
    } else {
      console.log("Table sales créée avec succès");
    }

    // 6. Créer la table promises
    console.log("Création de la table promises...");
    const { error: promisesError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'promises',
      columns: [
        'id uuid primary key default uuid_generate_v4()',
        'customer_id uuid not null references customers(id)',
        'telemarketer_id uuid not null references telemarketers(id)',
        'course_id uuid not null references courses(id)',
        'amount numeric not null',
        'due_date date not null',
        'original_due_date date not null',
        'reminders integer default 0',
        'status text not null default \'pending\'',
        'created_at timestamp with time zone default now()'
      ]
    });
    
    if (promisesError) {
      console.error("Erreur lors de la création de la table promises:", promisesError);
    } else {
      console.log("Table promises créée avec succès");
    }

    // 7. Créer la table advance_payments
    console.log("Création de la table advance_payments...");
    const { error: advancePaymentsError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'advance_payments',
      columns: [
        'id uuid primary key default uuid_generate_v4()',
        'sale_id uuid not null references sales(id)',
        'amount numeric not null',
        'payment_date date not null',
        'created_at timestamp with time zone default now()'
      ]
    });
    
    if (advancePaymentsError) {
      console.error("Erreur lors de la création de la table advance_payments:", advancePaymentsError);
    } else {
      console.log("Table advance_payments créée avec succès");
    }

    console.log("Création des tables terminée avec succès!");
  } catch (error) {
    console.error("Erreur lors de la création des tables:", error);
  }
}

// Exécuter la fonction de création des tables
createTables();
