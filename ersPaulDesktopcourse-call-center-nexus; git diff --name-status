[33mcommit f1129eae124845da87d743aaac62e6ed50248bd8[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mmain[m[33m, [m[1;31morigin/main[m[33m, [m[1;31morigin/HEAD[m[33m)[m
Author: Paolo068 <<EMAIL>>
Date:   Wed May 7 15:37:31 2025 +0100

    refactor: Remove Promises and Reminders functionality
    
    This commit removes all code related to Promises and Reminders from the application as requested.
    
    Changes include:
    - Removed pages: Promises.tsx and Reminders.tsx
    - Removed components: PromiseChart.tsx
    - Removed hooks: useSupabasePromises.ts and useSupabaseReminders.ts
    - Updated App.tsx to remove routes for /promises and /reminders
    - Updated Sidebar.tsx to remove navigation entries
    - Updated Dashboard.tsx to remove references to promises and reminders
    - Updated Sales.tsx to remove promise conversion functionality
    - Updated AddSaleDialog.tsx to remove promise options
    - Updated PrintReport.tsx to fix printing functionality
    - Updated types to remove promise and reminder related fields
    - Updated supabase/types.ts to simplify sale_status enum
    
    The application now focuses solely on sales functionality with proper XAF currency display.
