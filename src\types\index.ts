
export type TimePeriod = "day" | "week" | "month" | "all";

export type TelemarketerStats = {
  id: string;
  name: string;
  email?: string | null;
  phone?: string | null;
  promiseCount?: number;
  archived?: boolean;
  team_leader_id?: string | null;
  team_leader?: {
    id: string;
    name: string;
    email?: string | null;
    phone?: string | null;
  } | null;
}

export type TelemarketerPerformance = {
  name: string;
  sales: number;
  promises: number;
  reminders: number;
}
