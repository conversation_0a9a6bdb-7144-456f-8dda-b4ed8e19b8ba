// <PERSON><PERSON>t to clean all tables in the Supabase database
import { createClient } from '@supabase/supabase-js';

// Supabase connection details
const SUPABASE_URL = "https://vxzpeinztvurtncaaqdm.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ4enBlaW56dHZ1cnRuY2FhcWRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0ODY5MzksImV4cCI6MjA2MTA2MjkzOX0.EMJQUlI3yEwGzHCzuJcs8phfrEVDzJkavIdyKfbbk9s";

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// Tables to clean in the correct order (child tables first, then parent tables)
const tables = [
  'advance_payments',  // Must be deleted first as it references sales
  'promises',
  'reminders',
  'sales',            // Must be deleted before customers, telemarketers, and courses
  'customers',
  'telemarketers',
  'courses'
];

// We won't use the reverse order as it causes foreign key constraint issues

// Function to clean a table
async function cleanTable(tableName) {
  console.log(`Cleaning table: ${tableName}...`);

  try {
    // First get all rows to delete them one by one
    const { data, error: fetchError } = await supabase
      .from(tableName)
      .select('id');

    if (fetchError) {
      console.error(`Error fetching rows from ${tableName}:`, fetchError);
      return false;
    }

    if (data && data.length > 0) {
      console.log(`Found ${data.length} rows to delete in ${tableName}`);

      // Delete all rows one by one
      for (const row of data) {
        const { error: deleteError } = await supabase
          .from(tableName)
          .delete()
          .eq('id', row.id);

        if (deleteError) {
          console.error(`Error deleting row ${row.id} from ${tableName}:`, deleteError);
        }
      }
    } else {
      console.log(`No rows found in ${tableName}`);
    }

    console.log(`Table ${tableName} cleaned successfully.`);
    return true;
  } catch (err) {
    console.error(`Exception cleaning table ${tableName}:`, err);
    return false;
  }
}

// Clean all tables
async function cleanAllTables() {
  console.log('Starting to clean all tables...');

  // Clean tables in the defined order to respect foreign key constraints
  for (const table of tables) {
    await cleanTable(table);
  }

  console.log('All tables cleaned.');
}

// Run the cleaning process
cleanAllTables();
