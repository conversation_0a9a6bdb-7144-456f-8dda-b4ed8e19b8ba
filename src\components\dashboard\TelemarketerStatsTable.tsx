import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import * as React from "react";

interface TelemarketerPerformance {
  id?: string;
  name: string;
  sales: number;
  promises: number;
  reminders: number;
  advances: number;
  team_leader_id?: string | null;
  team_leader_name?: string | null;
}

interface TelemarketerStatsTableProps {
  data: TelemarketerPerformance[];
  title?: string;
  description?: string;
  isLoading?: boolean;
  groupByTeam?: boolean;
}

export function TelemarketerStatsTable({
  data,
  title = "Statistiques par Télévendeur",
  description = "Détails des performances par télévendeur",
  isLoading = false,
  groupByTeam = false
}: TelemarketerStatsTableProps) {
  // Calculer les totaux
  const totals = data.reduce(
    (acc, telemarketer) => {
      acc.sales += telemarketer.sales;
      acc.promises += telemarketer.promises;
      acc.advances += telemarketer.advances;
      acc.reminders += telemarketer.reminders;
      return acc;
    },
    { sales: 0, promises: 0, advances: 0, reminders: 0 }
  );

  // Organiser les données par équipe si nécessaire
  const teamData = React.useMemo(() => {
    if (!groupByTeam) return null;

    const teams = new Map<string, {
      name: string,
      members: TelemarketerPerformance[],
      sales: number,
      promises: number,
      advances: number,
      reminders: number
    }>();

    // Ajouter une équipe "Sans équipe" pour les télévendeurs sans chef d'équipe
    teams.set('no-team', {
      name: 'Sans équipe',
      members: [],
      sales: 0,
      promises: 0,
      advances: 0,
      reminders: 0
    });

    // Ajouter les chefs d'équipe comme équipes
    data.forEach(telemarketer => {
      if (telemarketer.team_leader_id === null || telemarketer.team_leader_id === undefined) {
        // Si c'est un chef d'équipe (pas de team_leader_id) et qu'il a des membres dans son équipe
        const isTeamLeader = data.some(t => t.team_leader_id === telemarketer.id);
        if (isTeamLeader) {
          teams.set(telemarketer.id || 'unknown', {
            name: telemarketer.name,
            members: [],
            sales: 0,
            promises: 0,
            advances: 0,
            reminders: 0
          });
        }
      }
    });

    // Ajouter les télévendeurs à leurs équipes respectives
    data.forEach(telemarketer => {
      const teamId = telemarketer.team_leader_id || 'no-team';
      if (teams.has(teamId)) {
        const team = teams.get(teamId)!;
        team.members.push(telemarketer);
        team.sales += telemarketer.sales;
        team.promises += telemarketer.promises;
        team.advances += telemarketer.advances;
        team.reminders += telemarketer.reminders;
      } else {
        // Si le chef d'équipe n'est pas dans la liste, ajouter à "Sans équipe"
        const noTeam = teams.get('no-team')!;
        noTeam.members.push(telemarketer);
        noTeam.sales += telemarketer.sales;
        noTeam.promises += telemarketer.promises;
        noTeam.advances += telemarketer.advances;
        noTeam.reminders += telemarketer.reminders;
      }
    });

    return Array.from(teams.values()).filter(team => team.members.length > 0);
  }, [data, groupByTeam]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-3">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{groupByTeam ? "Équipe / Télévendeur" : "Télévendeur"}</TableHead>
                <TableHead className="text-right">Ventes</TableHead>
                <TableHead className="text-right">Promesses</TableHead>
                <TableHead className="text-right">Avances</TableHead>
                <TableHead className="text-right">Rappels</TableHead>
                <TableHead className="text-right">Total</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {groupByTeam ? (
                // Affichage par équipe
                teamData?.map((team, teamIndex) => (
                  <React.Fragment key={`team-${teamIndex}`}>
                    {/* Ligne de l'équipe */}
                    <TableRow className="bg-muted/30">
                      <TableCell className="font-bold">{team.name}</TableCell>
                      <TableCell className="text-right font-bold">{team.sales}</TableCell>
                      <TableCell className="text-right font-bold">{team.promises}</TableCell>
                      <TableCell className="text-right font-bold">{team.advances}</TableCell>
                      <TableCell className="text-right font-bold">{team.reminders}</TableCell>
                      <TableCell className="text-right font-bold">
                        {team.sales + team.promises + team.advances}
                      </TableCell>
                    </TableRow>
                    {/* Lignes des membres de l'équipe */}
                    {team.members.map((member, memberIndex) => {
                      const total = member.sales + member.promises + member.advances;
                      return (
                        <TableRow key={`member-${teamIndex}-${memberIndex}`}>
                          <TableCell className="pl-6">{member.name}</TableCell>
                          <TableCell className="text-right">
                            {member.sales > 0 ? (
                              <Badge variant="outline" className="bg-status-paid text-white">
                                {member.sales}
                              </Badge>
                            ) : (
                              member.sales
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            {member.promises > 0 ? (
                              <Badge variant="outline" className="bg-status-promise text-white">
                                {member.promises}
                              </Badge>
                            ) : (
                              member.promises
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            {member.advances > 0 ? (
                              <Badge variant="outline" className="bg-status-advance text-white">
                                {member.advances}
                              </Badge>
                            ) : (
                              member.advances
                            )}
                          </TableCell>
                          <TableCell className="text-right">{member.reminders}</TableCell>
                          <TableCell className="text-right font-medium">{total}</TableCell>
                        </TableRow>
                      );
                    })}
                  </React.Fragment>
                ))
              ) : (
                // Affichage individuel
                data.map((telemarketer) => {
                  const total = telemarketer.sales + telemarketer.promises + telemarketer.advances;
                  return (
                    <TableRow key={telemarketer.id || telemarketer.name}>
                      <TableCell className="font-medium">{telemarketer.name}</TableCell>
                      <TableCell className="text-right">
                        {telemarketer.sales > 0 ? (
                          <Badge variant="outline" className="bg-status-paid text-white">
                            {telemarketer.sales}
                          </Badge>
                        ) : (
                          telemarketer.sales
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        {telemarketer.promises > 0 ? (
                          <Badge variant="outline" className="bg-status-promise text-white">
                            {telemarketer.promises}
                          </Badge>
                        ) : (
                          telemarketer.promises
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        {telemarketer.advances > 0 ? (
                          <Badge variant="outline" className="bg-status-advance text-white">
                            {telemarketer.advances}
                          </Badge>
                        ) : (
                          telemarketer.advances
                        )}
                      </TableCell>
                      <TableCell className="text-right">{telemarketer.reminders}</TableCell>
                      <TableCell className="text-right font-bold">{total}</TableCell>
                    </TableRow>
                  );
                })
              )}
              {/* Ligne des totaux */}
              <TableRow className="bg-muted/50">
                <TableCell className="font-bold">TOTAL</TableCell>
                <TableCell className="text-right font-bold">{totals.sales}</TableCell>
                <TableCell className="text-right font-bold">{totals.promises}</TableCell>
                <TableCell className="text-right font-bold">{totals.advances}</TableCell>
                <TableCell className="text-right font-bold">{totals.reminders}</TableCell>
                <TableCell className="text-right font-bold">
                  {totals.sales + totals.promises + totals.advances}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}
