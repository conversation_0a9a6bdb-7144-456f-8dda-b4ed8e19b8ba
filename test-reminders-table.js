// Script pour tester si la table reminders existe dans la base de données Supabase
import { createClient } from '@supabase/supabase-js';

// Supabase connection details
const SUPABASE_URL = "https://vxzpeinztvurtncaaqdm.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ4enBlaW56dHZ1cnRuY2FhcWRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0ODY5MzksImV4cCI6MjA2MTA2MjkzOX0.EMJQUlI3yEwGzHCzuJcs8phfrEVDzJkavIdyKfbbk9s";

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

async function testRemindersTable() {
  console.log("Test de la table reminders...");

  try {
    // Tester la requête de base
    const { data, error } = await supabase
      .from('reminders')
      .select('*')
      .limit(1);

    if (error) {
      console.error("❌ Erreur lors de l'accès à la table reminders:", error);
      console.log("La table reminders n'existe probablement pas dans la base de données.");
      
      // Tester les autres tables pour vérifier la connexion
      console.log("\nTest de connexion avec la table customers...");
      const { data: customersData, error: customersError } = await supabase
        .from('customers')
        .select('*')
        .limit(1);
      
      if (customersError) {
        console.error("❌ Erreur de connexion à la base de données:", customersError);
      } else {
        console.log("✅ Connexion à la base de données OK (table customers accessible)");
        console.log("Nombre de clients trouvés:", customersData?.length || 0);
      }
      
    } else {
      console.log("✅ Table reminders accessible!");
      console.log("Nombre de rappels trouvés:", data?.length || 0);
      if (data && data.length > 0) {
        console.log("Premier rappel:", data[0]);
      }
    }

  } catch (error) {
    console.error("❌ Erreur fatale:", error);
  }
}

// Exécuter le test
testRemindersTable()
  .then(() => {
    console.log("\nTest terminé");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Erreur fatale:", error);
    process.exit(1);
  });
