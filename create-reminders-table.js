// Script pour créer la table reminders manquante dans la base de données Supabase
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Supabase connection details
const SUPABASE_URL = "https://vxzpeinztvurtncaaqdm.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ4enBlaW56dHZ1cnRuY2FhcWRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0ODY5MzksImV4cCI6MjA2MTA2MjkzOX0.EMJQUlI3yEwGzHCzuJcs8phfrEVDzJkavIdyKfbbk9s";

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// Fonction pour créer la table reminders
async function createRemindersTable() {
  console.log("Création de la table reminders dans la base de données Supabase...");

  try {
    // Lire le fichier SQL
    const sqlContent = fs.readFileSync('create-reminders-table.sql', 'utf8');
    
    // Diviser le contenu en commandes individuelles
    const commands = sqlContent
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'));

    console.log(`Exécution de ${commands.length} commandes SQL...`);

    // Exécuter chaque commande
    for (let i = 0; i < commands.length; i++) {
      const command = commands[i];
      if (command.trim()) {
        console.log(`Exécution de la commande ${i + 1}/${commands.length}...`);
        
        const { data, error } = await supabase.rpc('exec_sql', {
          sql_query: command + ';'
        });

        if (error) {
          console.error(`Erreur lors de l'exécution de la commande ${i + 1}:`, error);
          // Continue avec les autres commandes même en cas d'erreur
        } else {
          console.log(`Commande ${i + 1} exécutée avec succès`);
        }
      }
    }

    // Vérifier que la table a été créée
    const { data: tables, error: checkError } = await supabase
      .from('reminders')
      .select('*')
      .limit(1);

    if (checkError) {
      console.error("Erreur lors de la vérification de la table reminders:", checkError);
    } else {
      console.log("✅ Table reminders créée et accessible avec succès!");
    }

  } catch (error) {
    console.error("Erreur lors de la création de la table reminders:", error);
  }
}

// Exécuter le script
createRemindersTable()
  .then(() => {
    console.log("Script terminé");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Erreur fatale:", error);
    process.exit(1);
  });
