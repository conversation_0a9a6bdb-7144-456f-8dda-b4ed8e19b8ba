import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import type { PromiseWithRelations } from "@/hooks/useSupabasePromises";
import { TimePeriod } from "@/types";
import { useState } from 'react';
import { Bar, BarChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

interface PromisesByTelemarketer {
  name: string;
  promises: number;
  salesPromises?: number;
}

interface PromiseChartProps {
  promisesData: PromiseWithRelations[];
}

export function PromiseChart({ promisesData = [] }: PromiseChartProps) {
  const [timePeriod, setTimePeriod] = useState<TimePeriod>("all");

  // Ensure promisesData is an array
  const safePromisesData = Array.isArray(promisesData) ? promisesData : [];

  // Process the data for the chart
  const filteredPromisesData = getPromisesDataByPeriod(safePromisesData, timePeriod);

  // Check if we have data to display
  const hasData = filteredPromisesData.length > 0;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Promesses par Télévendeur</CardTitle>
        <Select
          value={timePeriod}
          onValueChange={(value) => setTimePeriod(value as TimePeriod)}
        >
          <SelectTrigger className="w-36">
            <SelectValue placeholder="Sélectionner période" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="day">Aujourd'hui</SelectItem>
            <SelectItem value="week">Cette Semaine</SelectItem>
            <SelectItem value="month">Ce Mois</SelectItem>
            <SelectItem value="all">Tout</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent className="h-[300px]">
        {hasData ? (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={filteredPromisesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="promises" name="Promesses directes" fill="#fb7185" />
              <Bar dataKey="salesPromises" name="Promesses de vente" fill="#60a5fa" />
            </BarChart>
          </ResponsiveContainer>
        ) : (
          <div className="flex h-full items-center justify-center text-muted-foreground">
            Aucune promesse à afficher pour cette période
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Helper function to filter promises by time period
function getPromisesDataByPeriod(promises: PromiseWithRelations[], period: TimePeriod): PromisesByTelemarketer[] {
  if (!Array.isArray(promises) || promises.length === 0) return [];

  // Calculate the start date based on the selected period
  const now = new Date();
  let startDate = new Date(now);

  switch (period) {
    case "day":
      startDate.setHours(0, 0, 0, 0);
      break;
    case "week":
      startDate.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
      startDate.setHours(0, 0, 0, 0);
      break;
    case "month":
      startDate.setDate(1); // Start of month
      startDate.setHours(0, 0, 0, 0);
      break;
    case "all":
      startDate = new Date(0); // Beginning of time
      break;
  }

  console.log(`Filtering promises from ${startDate.toISOString()} for period ${period}`);

  // Filter promises by date
  const filteredPromises = promises.filter(promise => {
    if (!promise || !promise.due_date) return false;
    const promiseDate = new Date(promise.due_date);
    return promiseDate >= startDate;
  });

  console.log(`Filtered ${filteredPromises.length} promises for period ${period}`);

  // Process the filtered promises
  return promisesDataFromPromises(filteredPromises);
}

function promisesDataFromPromises(promises: PromiseWithRelations[]): PromisesByTelemarketer[] {
  if (!Array.isArray(promises) || promises.length === 0) return [];

  // Track regular promises and sales promises separately
  const telemarketerData = new Map<string, { promises: number; salesPromises: number }>();

  // Process each promise
  promises.forEach(promise => {
    if (!promise.telemarketers || !promise.telemarketers.name) return;

    const telemarketerName = promise.telemarketers.name;

    // Get or initialize data for this telemarketer
    const data = telemarketerData.get(telemarketerName) || { promises: 0, salesPromises: 0 };

    // Increment the appropriate counter based on the source
    if (promise.source === 'promise') {
      data.promises += 1;
    } else if (promise.source === 'sale') {
      data.salesPromises += 1;
    }

    // Update the map
    telemarketerData.set(telemarketerName, data);
  });

  // Convert the map to an array for the chart
  return Array.from(telemarketerData.entries()).map(([name, data]) => ({
    name,
    promises: data.promises,
    salesPromises: data.salesPromises
  }));
}
