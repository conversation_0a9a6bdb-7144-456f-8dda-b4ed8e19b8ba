import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Edit } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface Course {
  id: string;
  name: string;
  price: number;
}

interface EditCourseDialogProps {
  course: Course;
  onCourseUpdate: (course: { id: string; name: string; price: number }) => void;
}

export function EditCourseDialog({ course, onCourseUpdate }: EditCourseDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [editedCourse, setEditedCourse] = useState({
    id: course.id,
    name: course.name,
    price: course.price.toString(),
  });

  const handleUpdateCourse = () => {
    if (!editedCourse.name.trim()) {
      toast.error("Le nom de la formation est requis");
      return;
    }

    const price = parseFloat(editedCourse.price);
    if (isNaN(price) || price <= 0) {
      toast.error("Veuillez saisir un prix valide");
      return;
    }

    onCourseUpdate({
      id: editedCourse.id,
      name: editedCourse.name.trim(),
      price: price,
    });

    setIsOpen(false);
  };

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString("fr-FR", { style: "currency", currency: "XOF" });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button size="sm" variant="outline">
          <Edit className="h-4 w-4 mr-1" />
          Modifier
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Modifier la Formation</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="edit-name">Nom de la Formation</Label>
            <Input
              id="edit-name"
              value={editedCourse.name}
              onChange={(e) => setEditedCourse({ ...editedCourse, name: e.target.value })}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="edit-price">Prix</Label>
            <Input
              id="edit-price"
              type="number"
              value={editedCourse.price}
              onChange={(e) => setEditedCourse({ ...editedCourse, price: e.target.value })}
            />
            {editedCourse.price && !isNaN(parseFloat(editedCourse.price)) && (
              <div className="text-sm text-muted-foreground">
                {formatCurrency(parseFloat(editedCourse.price))}
              </div>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button type="submit" onClick={handleUpdateCourse}>Mettre à jour</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
