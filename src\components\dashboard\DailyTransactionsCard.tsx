import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { PromiseWithRelations } from "@/hooks/useSupabasePromises";
import { SaleWithRelations } from "@/hooks/useSupabaseSales";

interface DailyTransactionsCardProps {
  promises: PromiseWithRelations[];
  advances: SaleWithRelations[];
  isLoading?: boolean;
}

export function DailyTransactionsCard({ promises, advances, isLoading = false }: DailyTransactionsCardProps) {
  // Ajouter des logs pour voir les données reçues
  console.log("DailyTransactionsCard received promises:", promises);
  console.log("DailyTransactionsCard received advances:", advances);

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('fr-FR') + ' XAF';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Transactions</CardTitle>
        <CardDescription>Promesses et avances en cours</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="promises">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="promises">
              Promesses ({promises.length})
            </TabsTrigger>
            <TabsTrigger value="advances">
              Avances ({advances.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="promises" className="mt-4">
            {isLoading ? (
              <div className="space-y-3">
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
              </div>
            ) : promises.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Client</TableHead>
                    <TableHead>Télévendeur</TableHead>
                    <TableHead>Formation</TableHead>
                    <TableHead className="text-right">Montant</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {promises.map((promise) => (
                    <TableRow key={promise.id}>
                      <TableCell>{promise.customers?.name || 'N/A'}</TableCell>
                      <TableCell>{promise.telemarketers?.name || 'N/A'}</TableCell>
                      <TableCell>{promise.courses?.name || 'N/A'}</TableCell>
                      <TableCell className="text-right font-medium">
                        {formatCurrency(Number(promise.amount))}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                Aucune promesse en cours
              </div>
            )}
          </TabsContent>

          <TabsContent value="advances" className="mt-4">
            {isLoading ? (
              <div className="space-y-3">
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
              </div>
            ) : advances.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Client</TableHead>
                    <TableHead>Télévendeur</TableHead>
                    <TableHead>Formation</TableHead>
                    <TableHead className="text-right">Montant Total</TableHead>
                    <TableHead className="text-right">Avance</TableHead>
                    <TableHead>Date Limite</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {advances.map((advance) => (
                    <TableRow key={advance.id}>
                      <TableCell>{advance.customers?.name || 'N/A'}</TableCell>
                      <TableCell>{advance.telemarketers?.name || 'N/A'}</TableCell>
                      <TableCell>{advance.courses?.name || 'N/A'}</TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(Number(advance.price))}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(Number(advance.advance_amount || 0))}
                      </TableCell>
                      <TableCell>
                        {advance.due_date ? (
                          <span className={new Date(advance.due_date) < new Date() ? "text-red-600 font-bold" : ""}>
                            {new Date(advance.due_date).toLocaleDateString('fr-FR')}
                          </span>
                        ) : (
                          'N/A'
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                Aucune avance en cours
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
