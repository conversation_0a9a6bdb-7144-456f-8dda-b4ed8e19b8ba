
import { EditCourseDialog } from "@/components/courses/EditCourseDialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useSupabaseCourses } from "@/hooks/useSupabaseCourses";
import { PlusCircle } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export default function Courses() {
  const { courses, addCourse, updateCourse } = useSupabaseCourses();
  const [newCourse, setNewCourse] = useState({
    name: "",
    price: ""
  });

  const handleAddCourse = () => {
    const price = parseFloat(newCourse.price);
    if (!newCourse.name || isNaN(price)) {
      toast.error("Veuillez remplir tous les champs requis");
      return;
    }

    addCourse(
      { name: newCourse.name, price },
      {
        onSuccess: () => {
          setNewCourse({ name: "", price: "" });
          toast.success("Formation ajoutée avec succès");
        },
        onError: (error) => {
          toast.error("Erreur lors de l'ajout de la formation");
          console.error("Error adding course:", error);
        }
      }
    );
  };

  const handleUpdateCourse = (course: { id: string; name: string; price: number }) => {
    updateCourse(
      course,
      {
        onSuccess: () => {
          toast.success("Formation mise à jour avec succès");
        },
        onError: (error) => {
          toast.error("Erreur lors de la mise à jour de la formation");
          console.error("Error updating course:", error);
        }
      }
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Formations</h1>
        <Dialog>
          <DialogTrigger asChild>
            <Button>
              <PlusCircle className="mr-2 h-4 w-4" />
              Ajouter une Formation
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Ajouter une Nouvelle Formation</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Nom de la Formation</Label>
                <Input
                  id="name"
                  value={newCourse.name}
                  onChange={(e) => setNewCourse({ ...newCourse, name: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="price">Prix</Label>
                <Input
                  id="price"
                  type="number"
                  value={newCourse.price}
                  onChange={(e) => setNewCourse({ ...newCourse, price: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" onClick={handleAddCourse}>Enregistrer la Formation</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Toutes les Formations</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nom</TableHead>
                <TableHead>Prix</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {courses.map((course) => (
                <TableRow key={course.id}>
                  <TableCell className="font-medium">{course.name}</TableCell>
                  <TableCell>{course.price.toLocaleString("fr-FR")} XAF</TableCell>
                  <TableCell>
                    <EditCourseDialog
                      course={course}
                      onCourseUpdate={handleUpdateCourse}
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
