
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useState } from 'react';
import { Bar, BarChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import { SaleWithRelations } from "@/hooks/useSupabaseSales";

interface SalesByTelemarketer {
  name: string;
  sales: number;
}

interface SalesChartProps {
  salesData: SalesByTelemarketer[];
  allSales: SaleWithRelations[];
}

type TimePeriod = "day" | "week" | "month" | "all";

export function SalesChart({ salesData = [], allSales = [] }: SalesChartProps) {
  const [timePeriod, setTimePeriod] = useState<TimePeriod>("all");

  // Ensure salesData is an array
  const safeSalesData = Array.isArray(salesData) ? salesData : [];
  const safeAllSales = Array.isArray(allSales) ? allSales : [];

  const filteredSalesData = getSalesDataByPeriod(safeAllSales, timePeriod);
  
  console.log("Chart filtered sales data:", filteredSalesData);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Ventes par Télévendeur</CardTitle>
        <Select
          value={timePeriod}
          onValueChange={(value) => setTimePeriod(value as TimePeriod)}
        >
          <SelectTrigger className="w-36">
            <SelectValue placeholder="Sélectionner période" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="day">Aujourd'hui</SelectItem>
            <SelectItem value="week">Cette Semaine</SelectItem>
            <SelectItem value="month">Ce Mois</SelectItem>
            <SelectItem value="all">Tout</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={filteredSalesData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="sales" fill="#8B5CF6" />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}

function getSalesDataByPeriod(sales: SaleWithRelations[], period: TimePeriod): SalesByTelemarketer[] {
  if (!Array.isArray(sales) || sales.length === 0) {
    console.log("No sales data available for chart");
    return [];
  }

  console.log(`Processing ${sales.length} sales for period: ${period}`);

  const now = new Date();
  const startDate = new Date();

  // Filter sales by time period
  let filteredSales = sales;

  switch (period) {
    case "day":
      startDate.setHours(0, 0, 0, 0);
      filteredSales = sales.filter(sale => new Date(sale.date) >= startDate);
      break;
    case "week":
      startDate.setDate(now.getDate() - now.getDay()); // Start of current week (Sunday)
      startDate.setHours(0, 0, 0, 0);
      filteredSales = sales.filter(sale => new Date(sale.date) >= startDate);
      break;
    case "month":
      startDate.setDate(1); // Start of current month
      startDate.setHours(0, 0, 0, 0);
      filteredSales = sales.filter(sale => new Date(sale.date) >= startDate);
      break;
    // "all" case - use all sales
  }

  console.log(`Filtered to ${filteredSales.length} sales after date filtering`);

  // Only include paid and advance status sales for the chart
  filteredSales = filteredSales.filter(sale => 
    sale.status === "paid" || sale.status === "advance"
  );

  console.log(`Filtered to ${filteredSales.length} sales after status filtering`);

  // Count sales by telemarketer
  const telemarketerSales = new Map<string, number>();

  filteredSales.forEach(sale => {
    if (sale && sale.telemarketers && sale.telemarketers.name) {
      const telemarketerName = sale.telemarketers.name;
      const current = telemarketerSales.get(telemarketerName) || 0;
      telemarketerSales.set(telemarketerName, current + 1);
    }
  });

  console.log("Telemarketer sales counts:", Object.fromEntries(telemarketerSales));

  return Array.from(telemarketerSales.entries()).map(([name, sales]) => ({
    name,
    sales
  }));
}
