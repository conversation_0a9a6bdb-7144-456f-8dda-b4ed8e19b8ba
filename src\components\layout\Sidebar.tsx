
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { BarChart, Bell, BookOpen, Calendar, ChevronLeft, ChevronRight, Phone, User, Users } from "lucide-react";
import { useState } from "react";
import { Link, useLocation } from "react-router-dom";

const navItems = [
  {
    title: "Tableau de Bord",
    href: "/",
    icon: Bar<PERSON>hart,
  },
  {
    title: "Clients",
    href: "/customers",
    icon: User,
  },
  {
    title: "Télévendeurs",
    href: "/telemarketers",
    icon: Users,
  },
  {
    title: "Formations",
    href: "/courses",
    icon: BookOpen,
  },
  {
    title: "Ventes",
    href: "/sales",
    icon: Phone,
  },
  {
    title: "Promesses",
    href: "/promises",
    icon: Bell,
  },
  {
    title: "Rappels",
    href: "/reminders",
    icon: Calendar,
  },
];

export function Sidebar() {
  const location = useLocation();
  const [collapsed, setCollapsed] = useState(false);

  return (
    <div className={cn(
      "relative hidden border-r bg-card md:block transition-all duration-300",
      collapsed ? "md:w-16" : "md:w-64"
    )}>
      <div className="flex h-full flex-col">
        <div className={cn(
          "border-b",
          collapsed ? "p-2 flex justify-center" : "p-6"
        )}>
          {!collapsed && <h2 className="text-lg font-semibold">Centre d'Appel Formation</h2>}
        </div>
        <nav className="flex-1 space-y-1 p-2">
          {navItems.map((item) => (
            <Link
              key={item.href}
              to={item.href}
              className={cn(
                "flex items-center rounded-md px-3 py-2 text-sm font-medium",
                location.pathname === item.href
                  ? "bg-primary text-primary-foreground"
                  : "text-muted-foreground hover:bg-accent hover:text-accent-foreground",
                collapsed && "justify-center"
              )}
              title={collapsed ? item.title : ""}
            >
              <item.icon className={cn("h-5 w-5", !collapsed && "mr-3")} />
              {!collapsed && <span>{item.title}</span>}
            </Link>
          ))}
        </nav>
        <div className="border-t p-4 flex justify-center">
          {!collapsed && (
            <p className="text-xs text-muted-foreground">
              © {new Date().getFullYear()} Centre d'Appel Nexus
            </p>
          )}
        </div>
      </div>

      {/* Bouton pour réduire/agrandir la sidebar */}
      <Button
        variant="ghost"
        size="icon"
        className="absolute -right-3 top-20 h-6 w-6 rounded-full border bg-background shadow-md"
        onClick={() => setCollapsed(!collapsed)}
      >
        {collapsed ? <ChevronRight className="h-3 w-3" /> : <ChevronLeft className="h-3 w-3" />}
      </Button>
    </div>
  );
}
