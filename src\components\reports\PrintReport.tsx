
import { Button } from "@/components/ui/button";
import { Printer } from "lucide-react";
import { useRef } from "react";

interface Telemarketer {
  id: string;
  name: string;
  archived?: boolean;
}

interface PrintReportProps {
  type: "sales" | "promises";
  data: any[];
  period: string;
  selectedTelemarketer?: Telemarketer;
}

export function PrintReport({ type, data, period, selectedTelemarketer }: PrintReportProps) {
  const reportRef = useRef<HTMLDivElement>(null);

  // Log data for debugging
  console.log("PrintReport data:", data);

  const handlePrint = () => {
    const content = reportRef.current;
    if (!content) return;

    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert("Veuillez autoriser les popups pour imprimer le rapport");
      return;
    }

    printWindow.document.write(`
      <html>
        <head>
          <title>Rapport de ${type === "sales" ? "ventes" : "promesses"}</title>
          <style>
            body { font-family: Arial, sans-serif; }
            table { width: 100%; border-collapse: collapse; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            .header { display: flex; justify-content: space-between; align-items: center; }
            .title { font-size: 24px; font-weight: bold; }
            .subtitle { font-size: 18px; color: #666; }
            .date { font-size: 14px; color: #666; }
            .payment-info { color: #2563eb; font-weight: bold; }
          </style>
        </head>
        <body>
          <div class="header">
            <div>
              <div class="title">Rapport de ${type === "sales" ? "ventes" : "promesses"}</div>
              ${selectedTelemarketer ?
        `<div class="subtitle">Télévendeur: ${selectedTelemarketer.name}</div>` :
        '<div class="subtitle">Tous les télévendeurs</div>'}
            </div>
            <div class="date">
              Période: ${period === "day" ? "Aujourd'hui" :
        period === "week" ? "Cette semaine" :
          period === "month" ? "Ce mois" : "Tout"}
            </div>
          </div>
          <div>Date d'impression: ${new Date().toLocaleDateString("fr-FR", {
            day: "2-digit",
            month: "2-digit",
            year: "numeric",
            hour: "2-digit",
            minute: "2-digit"
          })}</div>
          <div>
            ${reportRef.current.innerHTML}
          </div>
        </body>
      </html>
    `);

    setTimeout(() => {
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
      printWindow.close();
    }, 500);
  };

  return (
    <div>
      <Button
        variant="outline"
        size="sm"
        onClick={handlePrint}
        className="flex gap-1 items-center"
      >
        <Printer className="h-4 w-4" />
        Imprimer
      </Button>
      <div className="hidden">
        <div ref={reportRef}>

          {type === "promises" ? (
            <table>
              <thead>
                <tr>
                  <th>Date d'échéance</th>
                  <th>Client</th>
                  <th>Formation</th>
                  <th>Montant</th>
                  <th>Télévendeur</th>
                  <th>Statut</th>
                  <th>Rappels</th>
                </tr>
              </thead>
              <tbody>
                {data.map((item: any) => (
                  <tr key={item.id}>
                    <td>{new Date(item.dueDate).toLocaleDateString("fr-FR")}</td>
                    <td>{item.customerName}</td>
                    <td>{item.courseName}</td>
                    <td>{item.amount.toLocaleString("fr-FR", { style: "currency", currency: "XOF" })}</td>
                    <td>{item.telemarketerName}</td>
                    <td>{
                      item.status === "pending" ? "En attente" :
                        item.status === "postponed" ? "Reportée" :
                          item.status === "reminder-sent" ? "Rappel envoyé" :
                            item.status === "fulfilled" ? "Réalisée" : "Inconnu"
                    }</td>
                    <td>{item.reminders}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <table>
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Client</th>
                  <th>Formation</th>
                  <th>Montant</th>
                  <th>Avances</th>
                  <th>Reste à payer</th>
                  <th>Télévendeur</th>
                  <th>Statut</th>
                </tr>
              </thead>
              <tbody>
                {Array.isArray(data) && data.length > 0 ? data.map((item: any) => {
                  const totalAdvances = item.advances?.reduce((sum: number, adv: any) => sum + Number(adv.amount), 0) || 0;
                  const remaining = Number(item.price) - totalAdvances;

                  return (
                    <tr key={item.id}>
                      <td>{new Date(item.date).toLocaleDateString("fr-FR")}</td>
                      <td>{item.customers?.name || item.customerName || "N/A"}</td>
                      <td>{item.courses?.name || item.courseName || "N/A"}</td>
                      <td>{Number(item.price).toLocaleString("fr-FR", { style: "currency", currency: "XOF" })}</td>
                      <td className="payment-info">
                        {item.status === "advance" ?
                          totalAdvances.toLocaleString("fr-FR", { style: "currency", currency: "XOF" }) :
                          "-"
                        }
                      </td>
                      <td className="payment-info">
                        {item.status === "advance" ?
                          remaining.toLocaleString("fr-FR", { style: "currency", currency: "XOF" }) :
                          "-"
                        }
                      </td>
                      <td>{item.telemarketers?.name || item.telemarketerName || "N/A"}</td>
                      <td>{
                        item.status === "paid" ? "Payé" :
                          item.status === "advance" ? "Avance" : "Inconnu"
                      }</td>
                    </tr>
                  );
                }) : (
                  <tr>
                    <td colSpan={8} className="text-center p-5">Aucune donnée disponible pour cette période</td>
                  </tr>
                )}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
}
