-- Script SQL pour ajouter la colonne team_leader_id à la table telemarketers

-- Vérifier si la colonne team_leader_id existe déjà
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'telemarketers'
    AND column_name = 'team_leader_id'
    AND table_schema = 'public'
  ) THEN
    -- Ajouter la colonne team_leader_id
    ALTER TABLE telemarketers ADD COLUMN team_leader_id UUID REFERENCES telemarketers(id);
    
    -- Ajouter un index pour améliorer les performances
    CREATE INDEX IF NOT EXISTS idx_telemarketers_team_leader_id ON telemarketers(team_leader_id);
    
    RAISE NOTICE 'Colonne team_leader_id ajoutée avec succès à la table telemarketers';
  ELSE
    RAISE NOTICE 'La colonne team_leader_id existe déjà dans la table telemarketers';
  END IF;
END
$$;
