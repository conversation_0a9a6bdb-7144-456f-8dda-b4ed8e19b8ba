
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useState } from 'react';
import { toast } from 'sonner';

interface SaleWithAdvances {
  id: string;
  price: number;
  original_price?: number;
  discount?: number;
  status: string;
  due_date?: string;
  advances?: {
    id: string;
    amount: number;
    payment_date: string;
  }[];
}

interface AdvancePaymentDialogProps {
  sale: SaleWithAdvances;
  onAddAdvance: (saleId: string, amount: number) => void;
  onMarkPaid: (saleId: string) => void;
}

export function AdvancePaymentDialog({
  sale,
  onAddAdvance,
  onMarkPaid
}: AdvancePaymentDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [amount, setAmount] = useState("");

  const totalAdvances = sale.advances?.reduce((sum, adv) => sum + Number(adv.amount), 0) || 0;
  const remaining = Number(sale.price) - totalAdvances;

  const getStatusBadge = () => {
    switch (sale.status) {
      case "advance":
        return (
          <Badge className="bg-status-advance">Avance</Badge>
        );
      case "paid":
        return <Badge className="bg-status-paid">Payé</Badge>;
      default:
        return <Badge>Inconnu</Badge>;
    }
  };

  const handleAddAdvance = () => {
    const advanceAmount = parseFloat(amount);

    if (isNaN(advanceAmount) || advanceAmount <= 0) {
      toast.error("Veuillez saisir un montant valide");
      return;
    }

    if (advanceAmount > remaining) {
      toast.error(`Le montant ne peut pas dépasser le solde restant (${remaining.toLocaleString('fr-FR')} XAF)`);
      return;
    }

    onAddAdvance(sale.id, advanceAmount);
    setAmount("");

    if (Math.abs(advanceAmount - remaining) < 0.01) {
      // If this advance completes the payment
      setIsOpen(false);
    }
  };

  const handleMarkPaid = () => {
    onMarkPaid(sale.id);
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" className="h-auto p-0">
          {getStatusBadge()}
        </Button>
      </DialogTrigger>
      <DialogContent onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>Paiement {sale.status === "advance" ? "Partiel" : "Complet"}</DialogTitle>
          <DialogDescription>
            Gérer le paiement pour cette vente
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-2">
          <div className="grid gap-2">
            <p className="font-medium">Montant total: {Number(sale.price).toLocaleString('fr-FR')} XAF</p>
            {sale.discount && sale.discount > 0 && sale.original_price && (
              <p className="text-green-600 text-sm">
                Réduction appliquée: {sale.discount}% (Prix original: {Number(sale.original_price).toLocaleString('fr-FR')} XAF)
              </p>
            )}

            {sale.status === "advance" && (
              <>
                <p className="text-green-600">Montant payé: {totalAdvances.toLocaleString('fr-FR')} XAF</p>
                <p className="text-red-600 font-medium">Reste à payer: {remaining.toLocaleString('fr-FR')} XAF</p>
                {sale.due_date && (
                  <p className="text-amber-600">
                    Date limite de paiement: {new Date(sale.due_date).toLocaleDateString('fr-FR')}
                    {new Date(sale.due_date) < new Date() && (
                      <span className="ml-2 text-red-600 font-bold">(En retard)</span>
                    )}
                  </p>
                )}

                {sale.advances && sale.advances.length > 0 && (
                  <div className="mt-2 border rounded p-2">
                    <p className="font-medium mb-1">Historique des paiements:</p>
                    {sale.advances.map((adv, i) => (
                      <div key={i} className="text-sm flex justify-between">
                        <span>{new Date(adv.payment_date).toLocaleDateString('fr-FR')}</span>
                        <span>{Number(adv.amount).toLocaleString('fr-FR')} XAF</span>
                      </div>
                    ))}
                  </div>
                )}
              </>
            )}
          </div>

          {sale.status === "advance" && (
            <div className="grid gap-2">
              <Label htmlFor="amount">Nouveau paiement</Label>
              <Input
                id="amount"
                type="number"
                min={0.01}
                step={0.01}
                max={remaining}
                placeholder={`${remaining.toLocaleString('fr-FR')} XAF max`}
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
              />
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          {sale.status === "advance" && (
            <>
              <Button variant="outline" onClick={handleMarkPaid}>
                Marquer comme payé intégralement
              </Button>
              <Button onClick={handleAddAdvance}>
                Ajouter le paiement
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
