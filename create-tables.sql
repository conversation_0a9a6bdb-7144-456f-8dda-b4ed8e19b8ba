-- Script SQL pour créer les tables dans la base de données Supabase

-- Activer l'extension uuid-ossp si elle n'est pas déjà activée
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Créer les types énumérés
DO $$
BEGIN
  -- Vérifier si le type promise_status existe déjà
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'promise_status') THEN
    CREATE TYPE promise_status AS ENUM ('pending', 'postponed', 'fulfilled', 'reminder-sent');
  END IF;
  
  -- Vérifier si le type reminder_status existe déjà
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'reminder_status') THEN
    CREATE TYPE reminder_status AS ENUM ('pending', 'completed', 'cancelled');
  END IF;
  
  -- Vérifier si le type sale_status existe déjà
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'sale_status') THEN
    CREATE TYPE sale_status AS ENUM ('paid', 'advance', 'promise', 'reminder');
  END IF;
END
$$;

-- Créer la table customers si elle n'existe pas
CREATE TABLE IF NOT EXISTS customers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Créer la table telemarketers si elle n'existe pas
CREATE TABLE IF NOT EXISTS telemarketers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  archived BOOLEAN DEFAULT FALSE,
  team_leader_id UUID REFERENCES telemarketers(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Créer la table courses si elle n'existe pas
CREATE TABLE IF NOT EXISTS courses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  price NUMERIC NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Créer la table sales si elle n'existe pas
CREATE TABLE IF NOT EXISTS sales (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID NOT NULL REFERENCES customers(id),
  telemarketer_id UUID NOT NULL REFERENCES telemarketers(id),
  course_id UUID NOT NULL REFERENCES courses(id),
  date DATE NOT NULL,
  price NUMERIC NOT NULL,
  original_price NUMERIC,
  discount NUMERIC DEFAULT 0,
  status TEXT NOT NULL DEFAULT 'paid',
  due_date DATE,
  advance_amount NUMERIC,
  advance_due_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Créer la table promises si elle n'existe pas
CREATE TABLE IF NOT EXISTS promises (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID NOT NULL REFERENCES customers(id),
  telemarketer_id UUID NOT NULL REFERENCES telemarketers(id),
  course_id UUID NOT NULL REFERENCES courses(id),
  amount NUMERIC NOT NULL,
  due_date DATE NOT NULL,
  original_due_date DATE,
  reminders INTEGER DEFAULT 0,
  status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Créer la table advance_payments si elle n'existe pas
CREATE TABLE IF NOT EXISTS advance_payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  sale_id UUID NOT NULL REFERENCES sales(id),
  amount NUMERIC NOT NULL,
  payment_date DATE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ajouter des index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_sales_customer_id ON sales(customer_id);
CREATE INDEX IF NOT EXISTS idx_sales_telemarketer_id ON sales(telemarketer_id);
CREATE INDEX IF NOT EXISTS idx_sales_course_id ON sales(course_id);
CREATE INDEX IF NOT EXISTS idx_sales_status ON sales(status);
CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(date);
CREATE INDEX IF NOT EXISTS idx_sales_due_date ON sales(due_date);

CREATE INDEX IF NOT EXISTS idx_promises_customer_id ON promises(customer_id);
CREATE INDEX IF NOT EXISTS idx_promises_telemarketer_id ON promises(telemarketer_id);
CREATE INDEX IF NOT EXISTS idx_promises_course_id ON promises(course_id);
CREATE INDEX IF NOT EXISTS idx_promises_status ON promises(status);
CREATE INDEX IF NOT EXISTS idx_promises_due_date ON promises(due_date);

CREATE INDEX IF NOT EXISTS idx_advance_payments_sale_id ON advance_payments(sale_id);
CREATE INDEX IF NOT EXISTS idx_advance_payments_payment_date ON advance_payments(payment_date);

CREATE INDEX IF NOT EXISTS idx_telemarketers_team_leader_id ON telemarketers(team_leader_id);
